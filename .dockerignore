# Git
.git
.gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
venv/
.venv/

# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# Environment files (except .env which is needed)
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.sqlite
.env.postgres
.env.mysql

# IDE
.idea/
.vscode/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Database
*.db
*.sqlite3

# Docker
docker-compose*.yml
Dockerfile*
.dockerignore

# Documentation
*.md
docs/

# Logs
logs/
*.log

# Backup
backup/

# Scripts
scripts/
easy-mcp.sh

# Data
data/
