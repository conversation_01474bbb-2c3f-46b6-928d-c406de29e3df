{"name": "easy-mcp-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"ant-design-vue": "4.0.0", "axios": "1.6.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "monaco-editor": "0.44.0", "pinia": "2.1.6", "vue": "3.3.4", "vue-router": "4.2.5", "vuedraggable": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "4.3.4", "eslint": "8.49.0", "eslint-plugin-vue": "9.17.0", "vite": "4.4.9"}}