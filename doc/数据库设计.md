# 数据库设计文档

## 1. 概述

本文档详细描述了 Easy MCP 系统的数据库设计，包括表结构、关系和索引策略。

## 2. 数据库技术

- **数据库系统**：SQLite（开发环境）/ PostgreSQL（生产环境推荐）/ MySQL（生产环境可选）
- **ORM 框架**：SQLModel（基于 SQLAlchemy 和 Pydantic）
- **数据库版本控制**：Alembic
- **异步支持**：aiosqlite / asyncpg / aiomysql
- **连接池**：SQLAlchemy 异步连接池
- **时间字段格式**：UnixMS（Unix时间戳的毫秒表示）

### 2.1 数据库选择指南

| 数据库 | 适用场景 | 优势 | 劣势 |
|--------|----------|------|------|
| SQLite | 开发环境、小型部署 | 零配置、轻量级、文件存储 | 并发限制、功能有限 |
| PostgreSQL | 生产环境（推荐） | 功能丰富、性能优秀、标准兼容 | 配置复杂、资源消耗较高 |
| MySQL | 生产环境（可选） | 广泛使用、性能良好、生态丰富 | 标准兼容性一般 |

## 3. 数据库设计规范

### 3.1 字段类型规范
- 字符串字段统一使用 `VARCHAR(255)` 或 `Text` 类型
- 时间字段统一使用 `UnixMS`（Unix时间戳的毫秒表示）
- 使用字符串类型存储Json数据，由业务层处理Json格式，保障数据库兼容性

### 3.2 标准字段要求
除审计日志表外，所有表都应包含以下标准字段：
- `created_by`: 创建人用户名
- `updated_by`: 更新人用户名
- `created_at`: 创建时间（UnixMS）
- `updated_at`: 更新时间（UnixMS）

### 3.3 关系处理
- 避免使用外键索引，仅在应用层面维护数据一致性
- 使用 `__table_args__` 和 Index 实现复合唯一约束

## 3. 表结构设计

### 3.1 用户表 (tb_user)

存储系统用户信息。

| 字段名 | 数据类型 | 约束 | 默认值 | 描述 |
|-------|---------|------|-------|------|
| id | INTEGER | PRIMARY KEY | 自增 | 用户ID |
| username | VARCHAR(255) | UNIQUE, NOT NULL | | 用户名 |
| password | VARCHAR(255) | NOT NULL | | 密码（加密存储） |
| email | VARCHAR(255) | NOT NULL | | 电子邮箱 |
| created_at | INTEGER | NOT NULL | | 创建时间（UnixMS） |
| updated_at | INTEGER | NOT NULL | | 更新时间（UnixMS） |
| created_by | VARCHAR(255) | | | 创建人名 |
| updated_by | VARCHAR(255) | | | 更新人名 |

索引：
- 主键索引：id
- 唯一索引：username

### 3.2 工具表 (tb_tool)

存储 MCP 工具的基本信息。

| 字段名 | 数据类型 | 约束 | 默认值 | 描述 |
|-------|---------|------|-----|------|
| id | INTEGER | PRIMARY KEY | 自增 | 工具ID |
| name | VARCHAR(255) | UNIQUE, NOT NULL | | 工具名称 |
| description | TEXT | | | 工具描述 |
| parameters | TEXT | NOT NULL | | 参数的JSON Schema |
| code | TEXT | NOT NULL | | 工具实现代码 |
| is_enabled | BOOLEAN | NOT NULL | TRUE | 是否启用 |
| current_version | INTEGER | | | 当前版本号 |
| created_at | INTEGER | NOT NULL | | 创建时间（UnixMS） |
| updated_at | INTEGER | NOT NULL | | 更新时间（UnixMS） |
| created_by | VARCHAR(255) | | | 创建人名 |
| updated_by | VARCHAR(255) | | | 更新人名 |

索引：
- 主键索引：id
- 唯一索引：name

### 3.3 工具发布历史表 (tb_tool_deploy)

记录工具的发布历史，支持版本控制。

| 字段名 | 数据类型 | 约束 | 默认值 | 描述 |
|-------|---------|------|-------|------|
| id | INTEGER | PRIMARY KEY | 自增 | 发布记录ID |
| tool_id | INTEGER | NOT NULL | | 工具ID，关联tb_tool.id |
| version | INTEGER | NOT NULL | | 版本号 |
| parameters | TEXT | NOT NULL | | 该版本的参数Schema |
| code | TEXT | NOT NULL | | 该版本的工具代码 |
| description | TEXT | | | 版本描述或变更说明 |
| created_at | INTEGER | NOT NULL | | 创建时间（UnixMS） |
| updated_at | INTEGER | NOT NULL | | 更新时间（UnixMS） |
| created_by | VARCHAR(255) | | | 创建人名 |
| updated_by | VARCHAR(255) | | | 更新人名 |

索引：
- 主键索引：id
- 复合唯一索引：(tool_id, version)

### 3.4 工具函数关联表 (tb_tool_func)

存储工具与函数库的关联关系。

| 字段名 | 数据类型 | 约束 | 默认值 | 描述 |
|-------|---------|------|-------|------|
| id | INTEGER | PRIMARY KEY | 自增 | 关联ID |
| tool_id | INTEGER | NOT NULL | | 工具ID，关联tb_tool.id |
| func_id | INTEGER | NOT NULL | | 函数ID，关联tb_func.id |
| created_at | INTEGER | NOT NULL | | 创建时间（UnixMS） |
| updated_at | INTEGER | NOT NULL | | 更新时间（UnixMS） |
| created_by | VARCHAR(255) | | | 创建人名 |
| updated_by | VARCHAR(255) | | | 更新人名 |

索引：
- 主键索引：id
- 复合唯一索引：(tool_id, func_id)

### 3.5 工具配置关联表 (tb_tool_config)

存储工具与配置的关联关系。

| 字段名 | 数据类型 | 约束 | 默认值 | 描述 |
|-------|---------|------|-------|------|
| id | INTEGER | PRIMARY KEY | 自增 | 关联ID |
| tool_id | INTEGER | NOT NULL | | 工具ID，关联tb_tool.id |
| config_id | INTEGER | NOT NULL | | 配置ID，关联tb_config.id |
| created_at | INTEGER | NOT NULL | | 创建时间（UnixMS） |
| updated_at | INTEGER | NOT NULL | | 更新时间（UnixMS） |
| created_by | VARCHAR(255) | | | 创建人名 |
| updated_by | VARCHAR(255) | | | 更新人名 |

索引：
- 主键索引：id
- 复合唯一索引：(tool_id, config_id)

### 3.6 函数库表 (tb_func)

存储可复用的函数库。

| 字段名 | 数据类型 | 约束 | 默认值 | 描述 |
|-------|---------|------|-----|------|
| id | INTEGER | PRIMARY KEY | 自增 | 函数ID |
| name | VARCHAR(255) | UNIQUE, NOT NULL | | 函数名称 |
| description | TEXT | | | 函数描述 |
| code | TEXT | NOT NULL | | 函数代码 |
| current_version | INTEGER | | | 当前版本号 |
| created_at | INTEGER | NOT NULL | | 创建时间（UnixMS） |
| updated_at | INTEGER | NOT NULL | | 更新时间（UnixMS） |
| created_by | VARCHAR(255) | | | 创建人名 |
| updated_by | VARCHAR(255) | | | 更新人名 |

索引：
- 主键索引：id
- 唯一索引：name

### 3.7 函数发布历史表 (tb_func_deploy)

记录函数的发布历史，支持版本控制。

| 字段名 | 数据类型 | 约束 | 默认值 | 描述 |
|-------|---------|------|-------|------|
| id | INTEGER | PRIMARY KEY | 自增 | 发布记录ID |
| func_id | INTEGER | NOT NULL | | 函数ID，关联tb_func.id |
| version | INTEGER | NOT NULL | | 版本号 |
| code | TEXT | NOT NULL | | 该版本的函数代码 |
| description | TEXT | | | 版本描述或变更说明 |
| created_at | INTEGER | NOT NULL | | 创建时间（UnixMS） |
| updated_at | INTEGER | NOT NULL | | 更新时间（UnixMS） |
| created_by | VARCHAR(255) | | | 创建人名 |
| updated_by | VARCHAR(255) | | | 更新人名 |

索引：
- 主键索引：id
- 复合唯一索引：(func_id, version)

### 3.8 函数依赖关系表 (tb_func_depends)

存储函数之间的依赖关系。

| 字段名 | 数据类型 | 约束 | 默认值 | 描述 |
|-------|---------|------|-------|------|
| id | INTEGER | PRIMARY KEY | 自增 | 依赖关系ID |
| func_id | INTEGER | NOT NULL | | 函数ID，关联tb_func.id |
| depends_on_func_id | INTEGER | NOT NULL | | 依赖的函数ID，关联tb_func.id |
| created_at | INTEGER | NOT NULL | | 创建时间（UnixMS） |
| updated_at | INTEGER | NOT NULL | | 更新时间（UnixMS） |
| created_by | VARCHAR(255) | | | 创建人名 |
| updated_by | VARCHAR(255) | | | 更新人名 |

索引：
- 主键索引：id
- 复合唯一索引：(func_id, depends_on_func_id)

### 3.9 配置表 (tb_config)

存储系统配置和工具配置模板。

| 字段名 | 数据类型 | 约束 | 默认值 | 描述 |
|-------|---------|------|-------|------|
| id | INTEGER | PRIMARY KEY | 自增 | 配置ID |
| name | VARCHAR(255) | UNIQUE, NOT NULL | | 配置名称 |
| description | TEXT | | | 配置描述 |
| conf_schema | TEXT | NOT NULL | | 配置的JSON Schema |
| conf_value | TEXT | | | 配置的具体值 |
| created_at | INTEGER | NOT NULL | | 创建时间（UnixMS） |
| updated_at | INTEGER | NOT NULL | | 更新时间（UnixMS） |
| created_by | VARCHAR(255) | | | 创建人名 |
| updated_by | VARCHAR(255) | | | 更新人名 |

索引：
- 主键索引：id
- 唯一索引：name

### 3.10 审计日志表 (tb_audit)

记录系统中的重要操作日志。

| 字段名 | 数据类型 | 约束 | 默认值 | 描述 |
|-------|---------|------|-------|------|
| id | INTEGER | PRIMARY KEY | 自增 | 日志ID |
| user_id | INTEGER | | | 操作用户ID |
| username | VARCHAR(255) | NOT NULL | | 操作用户名 |
| action | VARCHAR(255) | NOT NULL | | 操作类型 |
| resource_type | VARCHAR(255) | NOT NULL | | 资源类型 |
| resource_id | INTEGER | | | 资源ID |
| resource_name | VARCHAR(255) | | | 资源名称 |
| details | JSON | | | 操作详情 |
| ip_address | VARCHAR(255) | | | 操作IP地址 |
| created_at | INTEGER | NOT NULL | | 操作时间（UnixMS） |

索引：
- 主键索引：id
- 索引：action, resource_type, resource_id, resource_name, created_at


### 3.11 工具调用日志表 (tb_tool_log)

记录所有工具调用的详细信息，用于监控、调试和性能分析。

| 字段名 | 数据类型 | 约束 | 默认值 | 描述 |
|-------|---------|------|-------|------|
| id | INTEGER | PRIMARY KEY | 自增 | 日志ID |
| tool_id | INTEGER | | | 工具ID，关联tb_tool.id |
| tool_name | VARCHAR(255) | NOT NULL | | 工具名称 |
| call_type | VARCHAR(50) | NOT NULL | | 调用类型（mcp/debug） |
| request_time | INTEGER | NOT NULL | | 请求时间（UnixMS） |
| response_time | INTEGER | | | 响应时间（UnixMS） |
| duration_ms | INTEGER | | | 执行时间（毫秒） |
| is_success | BOOLEAN | NOT NULL | | 执行是否成功 |
| error_message | TEXT | | | 错误信息 |
| request_params | TEXT | | | 请求参数（JSON字符串） |
| response_data | TEXT | | | 响应数据（JSON字符串） |
| ip_address | VARCHAR(255) | | | 调用IP地址 |
| user_agent | VARCHAR(500) | | | 用户代理 |
| created_at | INTEGER | NOT NULL | | 创建时间（UnixMS） |

索引：
- 主键索引：id
- 索引：tool_id, call_type, is_success, created_at
- 复合索引：(tool_name, is_success), (call_type, created_at)

## 4. 实体关系图

```
tb_user <-- tb_audit (user_id)

tb_tool <-- tb_tool_deploy (tool_id)
tb_tool <-- tb_tool_func (tool_id)
tb_tool <-- tb_tool_config (tool_id)
tb_tool <-- tb_tool_log (tool_id)

tb_func <-- tb_func_deploy (func_id)
tb_func <-- tb_func_depends (func_id, depends_on_func_id)
tb_func <-- tb_tool_func (func_id)

tb_config <-- tb_tool_config (config_id)
```

## 5. 索引策略

1. **主键索引**：所有表都有自增的主键ID字段，并建立主键索引。
2. **唯一索引**：对需要唯一性约束的字段建立唯一索引。
3. **注意**：不设置外键索引，仅保留逻辑外键关系。

## 6. 数据迁移与版本控制

1. **数据库迁移工具**：使用Alembic进行数据库版本控制和迁移管理。
2. **迁移脚本**：每次数据库结构变更都会生成迁移脚本，记录变更内容。
3. **版本控制**：数据库版本与应用版本保持一致，确保兼容性。

## 7. 数据安全

1. **密码加密**：用户密码使用安全的哈希算法（如bcrypt）加密存储。
2. **敏感数据保护**：配置中的敏感信息（如API密钥）应加密存储。
3. **审计日志**：记录所有关键操作，便于安全审计和问题追踪。

## 8. 性能考虑

1. **分页查询**：所有列表查询都应支持分页，避免大量数据一次性加载。
2. **延迟加载**：使用SQLAlchemy的延迟加载特性，避免不必要的数据获取。
3. **查询优化**：针对复杂查询，使用适当的索引和查询优化技术。
4. **连接池**：使用数据库连接池，减少连接建立和断开的开销。

## 9. 资源删除处理

### 9.1 工具删除
删除工具时，需要清理以下关联数据：
- `tb_tool_deploy`: 工具发布历史
- `tb_tool_func`: 工具函数关联
- `tb_tool_config`: 工具配置关联
- `tb_tool_log`: 工具调用日志

### 9.2 函数删除
删除函数时，需要清理以下关联数据：
- `tb_tool_func`: 工具函数关联
- `tb_func_deploy`: 函数发布历史
- `tb_func_depends`: 函数依赖关系，包括：
  - 该函数依赖其他函数的关系（`func_id` 为该函数 ID）
  - 其他函数依赖该函数的关系（`depends_on_func_id` 为该函数 ID）
