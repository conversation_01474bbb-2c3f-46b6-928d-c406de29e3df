## 系统概述

Easy MCP 是一个动态 MCP (Model Context Protocol) 工具注册服务器，允许用户创建、管理和调试 MCP 工具。
该项目使用 Python 3.12、FastAPI 和 Vue3 构建，提供了一个完整的工具管理系统，包括函数代码编辑、工具调试、配置管理等功能。

## 技术栈

### 后端

- **Python 3.12**: 核心编程语言
- **FastAPI + Starlette + Uvicorn**: Web 框架和服务器
- **SQLModel**: ORM 框架（基于 SQLAlchemy 和 Pydantic）
- **MCP**: Model Context Protocol 库

### 前端

- **Vue 3**: 前端框架
- **Ant Design Vue**: UI 组件库
- **Vue Router**: 路由管理
- **Axios**： HTTP 请求库
- **Pinia**: 状态管理

## 系统设计

### 核心功能

1. **MCP 服务器**: 实现 MCP 协议的服务器，提供 SSE (Server-Sent Events) 服务
2. **工具管理**: 维护工具的参数、描述和实现代码等信息，支持版本控制
3. **函数管理**: 管理工具依赖的的函数代码库，支持版本控制
4. **配置管理**: 管理工具的配置信息
5. **用户管理**: 管理用户账户和认证
6. **审计日志**: 记录用户在系统中的操作日志，主要记录变更操作

### 数据库模型

系统使用 SQLModel 作为 ORM 框架，主要数据表包括：

- **tb_user**: 用户表
- **tb_tool**: 工具表
- **tb_tool_deploy**: 工具发布历史表
- **tb_tool_func**: 工具函数表
- **tb_tool_config**: 工具配置表
- **tb_tool_log**: 工具调用日志表
- **tb_func**: 函数库表
- **tb_func_deploy**: 函数发布历史表
- **tb_func_depends**: 函数依赖关系表
- **tb_config**: 配置表
- **tb_audit**: 审计日志表

## 系统功能

### MCP 服务器

Easy MCP 实现了一个符合 MCP 协议的服务器，提供以下功能：

- **/sse 端点**: 通过 SSE 协议与客户端通信
- **工具列表**: 动态生成可用工具列表
- **工具调用**: 执行工具代码并返回结果

### 工具管理

- **创建工具**: 创建新的 MCP 工具，指定名称、描述、参数和代码信息，以及绑定配置和设置依赖函数
- **编辑工具**: 修改工具的属性和配置
- **启用/禁用工具**: 控制工具的可用状态
- **调试工具**: 在界面中测试工具的执行结果
- **版本控制**: 记录工具的发布历史，支持回滚到之前的版本

### 函数管理

- **创建函数**: 编写新的函数代码，设置依赖的其它函数
- **编辑函数**: 修改现有函数代码
- **发布函数**: 将函数代码发布为新版本
- **版本控制**: 管理函数的版本历史，支持回滚到之前的版本

### 配置管理

- **创建配置**: 创建新的配置组，需要提供名称和配置的JSON Schema
- **编辑配置**: 修改现有配置信息
- **配置设置**: 根据配置的JSON Schema定义的配置来设置工具配置

### 用户管理

- **用户认证**: 基于 JWT 的用户认证
- **用户创建**: 创建新用户
- **密码管理**: 安全的密码存储和验证，密文存储

### 审计日志

- **操作记录**: 记录用户的关键操作
- **变更追踪**: 跟踪系统中的重要变更
- **日志查询**: 支持按多种条件查询审计日志

### 系统日志
- **查看日志**: 查看系统产生日志文件和内容
- **下载日志**: 下载系统日志文件

### 工具调用日志
- **调用记录**: 记录所有工具调用的详细信息
- **性能监控**: 跟踪工具执行时间和成功率
- **调试支持**: 提供详细的调用参数和返回结果

### OpenAPI 导入
- **Swagger 导入**: 支持从 OpenAPI/Swagger 规范导入工具
- **自动生成**: 根据 API 规范自动生成工具定义和参数
- **批量导入**: 一次性导入多个 API 端点作为工具

## 系统架构

### 整体架构

Easy MCP 采用前后端分离的架构设计：

```
┌─────────────────┐    ┌─────────────────┐    ┌───────────────────────┐
│   前端 (Vue3)   │    │  后端 (FastAPI) │    │  数据库 (Mysql、Postgres等)│
│                 │    │                 │    │                      │
│ - 用户界面      │◄──►│ - API 服务      │◄──►│ - 数据存储               │
│ - 状态管理      │    │ - 业务逻辑      │    │ - 关系管理               │
│ - 路由管理      │    │ - MCP 协议      │    │ - 索引优化               │
└─────────────────┘    └─────────────────┘    └───────────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │  MCP 客户端     │
                    │ (Cherry Studio) │
                    │                 │
                    │ - SSE 连接      │
                    │ - 工具调用      │
                    │ - 结果处理      │
                    └─────────────────┘
```
