version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: easy-mcp-postgres
    environment:
      POSTGRES_DB: easy_mcp
      POSTGRES_USER: easy_mcp
      POSTGRES_PASSWORD: easy_mcp_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../scripts/init-postgres.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U easy_mcp -d easy_mcp"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - easy-mcp-network

  easy-mcp:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: easy-mcp-app
    ports:
      - "8000:8000"
    volumes:
      - ../logs:/app/logs
    environment:
      - DB_URL=postgresql+asyncpg://easy_mcp:easy_mcp_password@postgres:5432/easy_mcp
      - DEBUG=false
      - LOG_LEVEL=INFO
      - HOST=0.0.0.0
      - PORT=8000
      - JWT_SECRET_KEY=easy_mcp_secret
      - CORS_ORIGINS=*
      - ADMIN_USERNAME=admin
      - ADMIN_PASSWORD=admin
      - ADMIN_EMAIL=<EMAIL>
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/system"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - easy-mcp-network

networks:
  easy-mcp-network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
