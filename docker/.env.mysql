# Easy MCP MySQL Environment Configuration

# Application Settings
APP_NAME=Easy MCP
APP_VERSION=0.1.0
DEBUG=false
LOG_LEVEL=INFO
LOG_CONFIG_PATH=api/logging.ini

# Server Settings
HOST=0.0.0.0
PORT=8000

# Database Settings - MySQL
DB_URL=mysql+aiomysql://easy_mcp:easy_mcp_password@mysql:3306/easy_mcp?charset=utf8mb4
DB_ECHO=false

# JWT Authentication
JWT_SECRET_KEY=easy_mcp_secret
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=1440  # 1 day

# CORS Settings
CORS_ORIGINS=*

# Admin User (created on first startup if not exists)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin
ADMIN_EMAIL=<EMAIL>

# Tool Execution Settings
TOOL_EXECUTION_TIMEOUT=30  # seconds
TOOL_MAX_MEMORY=512  # MB

# MySQL Database Settings
MYSQL_ROOT_PASSWORD=root_password
MYSQL_DATABASE=easy_mcp
MYSQL_USER=easy_mcp
MYSQL_PASSWORD=easy_mcp_password
