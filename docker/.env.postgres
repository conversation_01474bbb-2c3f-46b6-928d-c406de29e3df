# Easy MCP PostgreSQL Environment Configuration

# Application Settings
APP_NAME=Easy MCP
APP_VERSION=0.1.0
DEBUG=false
LOG_LEVEL=INFO
LOG_CONFIG_PATH=api/logging.ini

# Database Settings - PostgreSQL
DB_URL=postgresql+asyncpg://easy_mcp:easy_mcp_password@postgres:5432/easy_mcp
DB_ECHO=false

# JWT Authentication
JWT_SECRET_KEY=easy_mcp_secret
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=1440  # 1 day

# CORS Settings
CORS_ORIGINS=*

# Admin User (created on first startup if not exists)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin
ADMIN_EMAIL=<EMAIL>

# PostgreSQL Database Settings
POSTGRES_DB=easy_mcp
POSTGRES_USER=easy_mcp
POSTGRES_PASSWORD=easy_mcp_password
