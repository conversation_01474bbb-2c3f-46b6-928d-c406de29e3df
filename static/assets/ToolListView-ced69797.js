import{e as o,A as L,_ as Y,r as v,o as R,b as u,c as s,d as y,w as e,k as w,F as U,g as _,l as q,q as Z,u as K,h as N,f as c,P as ee,D as te,t as P,E as oe,s as ne,v as ae}from"./index-94aa741e.js";import{c as I,A as le,f as ie}from"./AppLayout-b20be4c8.js";import{P as se}from"./PauseCircleOutlined-24b40f6c.js";import{P as re}from"./PlayCircleOutlined-5272f431.js";import{A as ce}from"./ApiOutlined-37cdb302.js";var ue={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M811.4 418.7C765.6 297.9 648.9 212 512.2 212S258.8 297.8 213 418.6C127.3 441.1 64 519.1 64 612c0 110.5 89.5 200 199.9 200h496.2C870.5 812 960 722.5 960 612c0-92.7-63.1-170.7-148.6-193.3zm36.3 281a123.07 123.07 0 01-87.6 36.3H263.9c-33.1 0-64.2-12.9-87.6-36.3A123.3 123.3 0 01140 612c0-28 9.1-54.3 26.2-76.3a125.7 125.7 0 0166.1-43.7l37.9-9.9 13.9-36.6c8.6-22.8 20.6-44.1 35.7-63.4a245.6 245.6 0 0152.4-49.9c41.1-28.9 89.5-44.2 140-44.2s98.9 15.3 140 44.2c19.9 14 37.5 30.8 52.4 49.9 15.1 19.3 27.1 40.7 35.7 63.4l13.8 36.5 37.8 10c54.3 14.5 92.1 63.8 92.1 120 0 33.1-12.9 64.3-36.3 87.7z"}}]},name:"cloud",theme:"outlined"};const de=ue;function F(l){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},r=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(d){return Object.getOwnPropertyDescriptor(n,d).enumerable}))),r.forEach(function(d){pe(l,d,n[d])})}return l}function pe(l,t,n){return t in l?Object.defineProperty(l,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):l[t]=n,l}var j=function(t,n){var r=F({},t,n.attrs);return o(L,F({},r,{icon:de}),null)};j.displayName="CloudOutlined";j.inheritAttrs=!1;const me=j;var _e={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM653.3 424.6l52.2 52.2a8.01 8.01 0 01-4.7 13.6l-179.4 21c-5.1.6-9.5-3.7-8.9-8.9l21-179.4c.8-6.6 8.9-9.4 13.6-4.7l52.4 52.4 256.2-256.2c3.1-3.1 8.2-3.1 11.3 0l42.4 42.4c3.1 3.1 3.1 8.2 0 11.3L653.3 424.6z"}}]},name:"import",theme:"outlined"};const fe=_e;function H(l){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},r=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(d){return Object.getOwnPropertyDescriptor(n,d).enumerable}))),r.forEach(function(d){ve(l,d,n[d])})}return l}function ve(l,t,n){return t in l?Object.defineProperty(l,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):l[t]=n,l}var B=function(t,n){var r=H({},t,n.attrs);return o(L,H({},r,{icon:fe}),null)};B.displayName="ImportOutlined";B.inheritAttrs=!1;const ge=B;const ye={key:0,class:"empty-state"},he={__name:"ImportToolDialog",props:{visible:{type:Boolean,default:!1}},emits:["update:visible","imported"],setup(l,{emit:t}){const n=l,r=p=>{p||(g.value=[])},d=[{title:"工具名称",dataIndex:"name",key:"name"},{title:"描述",dataIndex:"description",key:"description"},{title:"配置",key:"hasConfig"}],h=v([]),k=v(!1),O=v(!1),g=v([]),A=async()=>{k.value=!0;try{await I({method:"get",url:"/api/v1/tool-builtin",onSuccess:p=>{h.value=(p==null?void 0:p.tools)||[]}})}catch(p){console.error("Failed to load builtin tools:",p)}finally{k.value=!1}},b=p=>{g.value=p},D=async()=>{if(g.value.length===0){Z.warning("请选择要导入的工具");return}O.value=!0;try{await I({method:"post",url:"/api/v1/tool-builtin/import",data:{tool_id:g.value[0]},successMessage:"工具导入成功",onSuccess:()=>{t("imported"),z()}})}catch(p){console.error("Failed to import tool:",p)}finally{O.value=!1}},z=()=>{g.value=[],t("update:visible",!1)};return R(()=>{r(n.visible),A()}),(p,E)=>{const T=u("a-empty"),f=u("a-tag"),a=u("a-table"),$=u("a-spin"),C=u("a-modal");return s(),y(C,{open:l.visible,title:"导入内置工具","confirm-loading":O.value,width:700,"ok-text":"确定","cancel-text":"取消",onOk:D,onCancel:z},{default:e(()=>[o($,{spinning:k.value},{default:e(()=>[h.value.length===0&&!k.value?(s(),w("div",ye,[o(T,{description:"没有可用的内置工具"})])):(s(),y(a,{key:1,columns:d,"data-source":h.value,pagination:!1,"row-selection":{selectedRowKeys:g.value,onChange:b,type:"radio"},"row-key":S=>S.id},{bodyCell:e(({column:S,record:M})=>[S.key==="hasConfig"?(s(),w(U,{key:0},[M.has_config?(s(),y(f,{key:0,color:"blue"},{default:e(()=>[_("有配置")]),_:1})):(s(),y(f,{key:1,color:"green"},{default:e(()=>[_("无配置")]),_:1}))],64)):q("",!0)]),_:1},8,["data-source","row-selection","row-key"]))]),_:1},8,["spinning"])]),_:1},8,["open","confirm-loading"])}}},ke=Y(he,[["__scopeId","data-v-2bc8fd8b"]]),be={class:"action-bar"},Ce=["onClick"],Oe={key:1},we={key:1},$e={key:4},Te={__name:"ToolListView",setup(l){const t=K(),n=v(!1),r=v([]),d=v(0),h=v(1),k=v(10),O=v(""),g=v(!1),A=[{title:"名称",dataIndex:"name",key:"name",width:160,ellipsis:!0,tooltip:!0},{title:"描述",dataIndex:"description",key:"description",width:200,ellipsis:!0,tooltip:!0},{title:"状态",key:"status",width:100,ellipsis:!0,tooltip:!0},{title:"版本",key:"version",width:100,ellipsis:!0,tooltip:!0},{title:"创建时间",key:"created_at",width:160,ellipsis:!0,tooltip:!0},{title:"操作",key:"action",width:280,fixed:"right"}];R(()=>{b()});const b=async()=>{n.value=!0;try{await I({method:"get",url:"/api/v1/tool",params:{page:h.value,size:k.value,search:O.value||void 0},onSuccess:(f,a)=>{r.value=f,d.value=a.total},errorMessage:"获取工具列表失败"})}finally{n.value=!1}},D=()=>{h.value=1,b()},z=(f,a)=>{h.value=f,k.value=a,b()},p=(f,a)=>{h.value=1,k.value=a,b()},E=async f=>{try{await I({method:"delete",url:`/api/v1/tool/${f}`,successMessage:"删除成功",errorMessage:"删除失败"}),b()}catch(a){console.error("Error deleting tool:",a)}},T=async(f,a)=>{try{await I({method:"patch",url:`/api/v1/tool/${f}/${a?"enable":"disable"}`,successMessage:`${a?"启用":"禁用"}成功`,errorMessage:`${a?"启用":"禁用"}失败`}),b()}catch($){console.error(`Error ${a?"enabling":"disabling"} tool:`,$)}};return(f,a)=>{const $=u("a-input-search"),C=u("a-button"),S=u("a-menu-item"),M=u("a-menu"),G=u("a-dropdown"),V=u("a-tag"),J=u("a-popconfirm"),Q=u("a-space"),W=u("a-table"),X=u("a-card");return s(),y(le,{"current-page-key":"tool"},{default:e(()=>[o(X,{title:"工具管理"},{default:e(()=>[N("div",be,[o($,{value:O.value,"onUpdate:value":a[0]||(a[0]=i=>O.value=i),placeholder:"搜索工具名称或描述",style:{width:"300px"},onSearch:D},null,8,["value"]),N("div",null,[o(C,{type:"primary",style:{"margin-right":"8px"},onClick:a[1]||(a[1]=i=>c(t).push("/tool/create"))},{icon:e(()=>[o(c(ee))]),default:e(()=>[_(" 创建工具 ")]),_:1}),o(G,null,{overlay:e(()=>[o(M,null,{default:e(()=>[o(S,{key:"1",onClick:a[2]||(a[2]=i=>g.value=!0)},{default:e(()=>[o(c(ce)),_(" 导入内置工具 ")]),_:1}),o(S,{key:"2",onClick:a[3]||(a[3]=i=>c(t).push("/tool/import-openapi"))},{default:e(()=>[o(c(me)),_(" 导入OpenAPI ")]),_:1})]),_:1})]),default:e(()=>[o(C,null,{icon:e(()=>[o(c(ge))]),default:e(()=>[_(" 导入工具 "),o(c(te))]),_:1})]),_:1})])]),o(W,{columns:A,"data-source":r.value,loading:n.value,pagination:{current:h.value,pageSize:k.value,total:d.value,onChange:z,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:p,showTotal:i=>`共 ${i} 条记录`},"row-key":i=>i.id},{bodyCell:e(({column:i,record:m})=>[i.key==="name"?(s(),w("a",{key:0,onClick:x=>c(t).push(`/tool/${m.id}`)},P(m.name),9,Ce)):i.key==="description"?(s(),w("span",Oe,P(m.description||"无描述"),1)):i.key==="status"?(s(),y(V,{key:2,color:m.is_enabled?"green":"red"},{default:e(()=>[_(P(m.is_enabled?"已启用":"已禁用"),1)]),_:2},1032,["color"])):i.key==="version"?(s(),w(U,{key:3},[m.current_version?(s(),y(V,{key:0,color:"blue"},{default:e(()=>[_("v"+P(m.current_version),1)]),_:2},1024)):(s(),w("span",we,"未发布"))],64)):i.key==="created_at"?(s(),w("span",$e,P(c(ie)(m.created_at)),1)):i.key==="action"?(s(),y(Q,{key:5},{default:e(()=>[o(C,{type:"link",size:"small",onClick:x=>c(t).push(`/tool/${m.id}`)},{icon:e(()=>[o(c(oe))]),default:e(()=>[_(" 查看 ")]),_:2},1032,["onClick"]),o(C,{type:"link",size:"small",onClick:x=>c(t).push(`/tool/${m.id}/edit`)},{icon:e(()=>[o(c(ne))]),default:e(()=>[_(" 编辑 ")]),_:2},1032,["onClick"]),m.is_enabled?(s(),y(C,{key:0,type:"link",size:"small",onClick:x=>T(m.id,!1)},{icon:e(()=>[o(c(se))]),default:e(()=>[_(" 禁用 ")]),_:2},1032,["onClick"])):(s(),y(C,{key:1,type:"link",size:"small",onClick:x=>T(m.id,!0)},{icon:e(()=>[o(c(re))]),default:e(()=>[_(" 启用 ")]),_:2},1032,["onClick"])),o(J,{title:"确定要删除此工具吗？","ok-text":"确定","cancel-text":"取消",onConfirm:x=>E(m.id)},{default:e(()=>[o(C,{type:"link",size:"small",danger:""},{icon:e(()=>[o(c(ae))]),default:e(()=>[_(" 删除 ")]),_:1})]),_:2},1032,["onConfirm"])]),_:2},1024)):q("",!0)]),_:1},8,["data-source","loading","pagination","row-key"])]),_:1}),o(ke,{visible:g.value,"onUpdate:visible":a[4]||(a[4]=i=>g.value=i),onImported:b},null,8,["visible"])]),_:1})}}};export{Te as default};
