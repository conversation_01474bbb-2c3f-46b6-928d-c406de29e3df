import{_ as W,u as G,x as Q,y as S,r as i,a as X,o as Y,b as r,c as Z,d as ee,w as s,e as o,f as h,g as b,t as ae,h as m,q as _}from"./index-94aa741e.js";import{c as l,A as oe,v as te}from"./AppLayout-b20be4c8.js";import{_ as se}from"./MonacoEditor-7a57fdbb.js";import{J as ne}from"./JsonSchemaEditor-0d8e4465.js";import{R as re}from"./RollbackOutlined-27c22304.js";import{S as le}from"./SaveOutlined-9c7136e5.js";import{C as ie}from"./CloudUploadOutlined-ca8531ec.js";/* empty css                                                     */const ce={class:"tab-description"},ue={class:"schema-editor-container"},de={class:"tab-description"},pe={class:"editor-container"},fe={__name:"ToolEditView",setup(me){const U=G(),E=Q(),c=S(()=>E.params.id),d=S(()=>!!c.value),u=i(!1),w=i(!1),v=i(!1),g=i(!1),M=i("parameters"),a=X({name:"",description:"",parametersStr:JSON.stringify({type:"object",properties:{name:{type:"string",description:"名称"}},required:["name"]},null,2),code:`# parameters: 传入参数
# config: 传入配置
# result: 用于返回值

# 示例代码：
print("执行工具...")
result = {"message": "Hello, World!", "parameters": parameters, "config": config}
`,func_ids:[],config_ids:[]}),O=i([]),k=i([]);S(()=>{try{const e=JSON.parse(a.parametersStr);return JSON.stringify(e,null,2)}catch{return"无效的 JSON"}}),Y(async()=>{await Promise.all([V(),$()]),d.value&&await B()});const V=async()=>{v.value=!0;try{await l({method:"get",url:"/api/v1/func",params:{page:1,size:100},onSuccess:e=>{O.value=e.map(t=>({value:t.id,label:t.name}))},errorMessage:"获取函数列表失败"})}finally{v.value=!1}},$=async()=>{g.value=!0;try{await l({method:"get",url:"/api/v1/config",params:{page:1,size:100},onSuccess:e=>{k.value=e.map(t=>({value:t.id,label:t.name}))},errorMessage:"获取配置列表失败"})}finally{g.value=!1}},B=async()=>{w.value=!0;try{await l({method:"get",url:`/api/v1/tool/${c.value}`,onSuccess:e=>{a.name=e.name,a.description=e.description||"",a.parametersStr=JSON.stringify(e.parameters,null,2),a.code=e.code,q(),D()},errorMessage:"获取工具数据失败"})}finally{w.value=!1}},q=async()=>{try{await l({method:"get",url:`/api/v1/tool/${c.value}/func`,onSuccess:e=>{e&&Array.isArray(e)?a.func_ids=e.map(t=>t.id):a.func_ids=[]},errorMessage:"获取工具依赖函数失败"})}catch(e){console.error("Error fetching tool dependent functions:",e)}},D=async()=>{try{await l({method:"get",url:`/api/v1/tool/${c.value}/config`,onSuccess:e=>{e&&Array.isArray(e)?a.config_ids=e.map(t=>t.id):a.config_ids=[]},errorMessage:"获取工具绑定配置失败"})}catch(e){console.error("Error fetching tool bound configs:",e)}},J=()=>{if(!a.name)return _.error("请输入工具名称"),!1;if(!a.description)return _.error("请输入工具描述"),!1;const e=te(a.parametersStr);return e.valid?a.code?!0:(_.error("请输入工具代码"),!1):(_.error("参数定义 JSON 格式不正确: "+e.message),!1)},N=()=>({name:a.name,description:a.description,parameters:JSON.parse(a.parametersStr),code:a.code,func_ids:a.func_ids,config_ids:a.config_ids}),F=async()=>{if(J()){u.value=!0;try{const e=N();d.value?await l({method:"put",url:`/api/v1/tool/${c.value}`,data:e,successMessage:"保存成功",errorMessage:"保存失败",onSuccess:()=>{p()}}):await l({method:"post",url:"/api/v1/tool",data:e,successMessage:"创建成功",errorMessage:"创建失败",onSuccess:()=>{p()}})}finally{u.value=!1}}},K=async()=>{if(J()){u.value=!0;try{const e=N();d.value?await l({method:"put",url:`/api/v1/tool/${c.value}/deploy`,data:e,successMessage:"保存并发布成功",errorMessage:"保存并发布失败",onSuccess:()=>{p()}}):await l({method:"post",url:"/api/v1/tool/deploy",data:e,successMessage:"创建并发布成功",errorMessage:"创建并发布失败",onSuccess:()=>{p()}})}finally{u.value=!1}}},p=()=>{U.back()};return(e,t)=>{const y=r("a-button"),L=r("a-space"),R=r("a-input"),f=r("a-form-item"),z=r("a-textarea"),x=r("a-select"),C=r("a-col"),I=r("a-row"),A=r("a-alert"),T=r("a-tab-pane"),P=r("a-tabs"),j=r("a-form"),H=r("a-card");return Z(),ee(oe,{"current-page-key":"tool"},{default:s(()=>[o(H,{title:d.value?"编辑工具":"创建工具"},{extra:s(()=>[o(L,null,{default:s(()=>[o(y,{onClick:p},{icon:s(()=>[o(h(re))]),default:s(()=>[b(" 返回 ")]),_:1}),o(y,{type:"primary",loading:u.value,onClick:F},{icon:s(()=>[o(h(le))]),default:s(()=>[b(" 保存 ")]),_:1},8,["loading"]),o(y,{type:"primary",loading:u.value,onClick:K},{icon:s(()=>[o(h(ie))]),default:s(()=>[b(" "+ae(d.value?"保存并发布":"创建并发布"),1)]),_:1},8,["loading"])]),_:1})]),default:s(()=>[o(j,{model:a,layout:"vertical",class:"form-container"},{default:s(()=>[o(f,{label:"工具名称",required:""},{default:s(()=>[o(R,{value:a.name,"onUpdate:value":t[0]||(t[0]=n=>a.name=n),placeholder:"请输入工具名称"},null,8,["value"])]),_:1}),o(f,{label:"工具描述",required:""},{default:s(()=>[o(z,{value:a.description,"onUpdate:value":t[1]||(t[1]=n=>a.description=n),placeholder:"请输入工具描述",rows:3},null,8,["value"])]),_:1}),o(I,{gutter:16},{default:s(()=>[o(C,{span:12},{default:s(()=>[o(f,{label:"依赖函数"},{default:s(()=>[o(x,{value:a.func_ids,"onUpdate:value":t[2]||(t[2]=n=>a.func_ids=n),mode:"multiple",placeholder:"请选择依赖函数",loading:v.value,options:O.value},null,8,["value","loading","options"])]),_:1})]),_:1}),o(C,{span:12},{default:s(()=>[o(f,{label:"绑定配置"},{default:s(()=>[o(x,{value:a.config_ids,"onUpdate:value":t[3]||(t[3]=n=>a.config_ids=n),mode:"multiple",placeholder:"请选择绑定配置",loading:g.value,options:k.value},null,8,["value","loading","options"])]),_:1})]),_:1})]),_:1}),o(f,null,{default:s(()=>[o(P,{activeKey:M.value,"onUpdate:activeKey":t[6]||(t[6]=n=>M.value=n)},{default:s(()=>[o(T,{key:"parameters",tab:"参数定义","force-render":""},{default:s(()=>[m("div",ce,[o(A,{message:"参数定义说明",description:"定义工具的输入参数。您可以使用图形化编辑器或直接编辑JSON Schema。",type:"info","show-icon":""})]),m("div",ue,[o(ne,{value:a.parametersStr,"onUpdate:value":t[4]||(t[4]=n=>a.parametersStr=n)},null,8,["value"])])]),_:1}),o(T,{key:"code",tab:"工具代码","force-render":""},{default:s(()=>[m("div",de,[o(A,{message:"工具代码说明",description:"编写工具的执行代码。系统会自动提供以下变量：parameters（传入的参数）、config（绑定的配置）、result（用于返回结果）。您可以使用依赖函数和Python标准库。",type:"info","show-icon":""})]),m("div",pe,[o(se,{value:a.code,"onUpdate:value":t[5]||(t[5]=n=>a.code=n),language:"python",options:{automaticLayout:!0,scrollBeyondLastLine:!1}},null,8,["value"])])]),_:1})]),_:1},8,["activeKey"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title"])]),_:1})}}},Me=W(fe,[["__scopeId","data-v-3fd7fc33"]]);export{Me as default};
