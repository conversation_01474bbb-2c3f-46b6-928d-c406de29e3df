import{_ as H,u as J,r as l,o as K,b as u,c as r,d as O,w as t,e as a,h as Q,f as s,P as W,g as f,k as p,t as $,E as X,s as Y,v as Z,l as E,F as ee}from"./index-94aa741e.js";import{c as z,A as ae,f as te,S as ne}from"./AppLayout-b20be4c8.js";import{_ as oe}from"./JsonSchemaForm-58703916.js";const le={class:"action-bar"},se=["onClick"],ie={key:1},ce={key:2},ue={key:0,class:"empty-schema"},re={key:1},de={__name:"ConfigListView",setup(pe){const g=J(),C=l(!1),b=l([]),V=l(0),_=l(1),m=l(10),w=l(""),h=l(!1),i=l(null),x=l(!1),y=l({}),L=[{title:"名称",dataIndex:"name",key:"name",width:160,ellipsis:!0,tooltip:!0},{title:"描述",dataIndex:"description",key:"description",width:200,ellipsis:!0,tooltip:!0},{title:"创建时间",key:"created_at",width:160},{title:"操作",key:"action",width:200,fixed:"right"}];K(()=>{d()});const d=async()=>{C.value=!0;try{await z({method:"get",url:"/api/v1/config",params:{page:_.value,size:m.value,search:w.value||void 0},onSuccess:(o,e)=>{b.value=o,V.value=e.total},errorMessage:"获取配置列表失败"})}finally{C.value=!1}},P=()=>{_.value=1,d()},B=(o,e)=>{_.value=o,m.value=e,d()},F=(o,e)=>{_.value=1,m.value=e,d()},N=async o=>{try{await z({method:"delete",url:`/api/v1/config/${o}`,successMessage:"删除成功",errorMessage:"删除失败"}),d()}catch(e){console.error("Error deleting config:",e)}},T=o=>{i.value=o,y.value=o.conf_value||{},h.value=!0},S=()=>{h.value=!1,i.value=null,y.value={}},A=async o=>{if(i.value){x.value=!0;try{await z({method:"put",url:`/api/v1/config/${i.value.id}/value`,data:{conf_value:o},successMessage:"配置保存成功",errorMessage:"配置保存失败"}),d(),S()}catch(e){console.error("Error saving config value:",e)}finally{x.value=!1}}};return(o,e)=>{const D=u("a-input-search"),v=u("a-button"),I=u("a-popconfirm"),U=u("a-space"),j=u("a-table"),R=u("a-empty"),q=u("a-modal"),G=u("a-card");return r(),O(ae,{"current-page-key":"config"},{default:t(()=>[a(G,{title:"配置管理"},{default:t(()=>{var M;return[Q("div",le,[a(D,{value:w.value,"onUpdate:value":e[0]||(e[0]=n=>w.value=n),placeholder:"搜索配置名称或描述",style:{width:"300px"},onSearch:P},null,8,["value"]),a(v,{type:"primary",onClick:e[1]||(e[1]=n=>s(g).push("/config/create"))},{icon:t(()=>[a(s(W))]),default:t(()=>[f(" 创建配置 ")]),_:1})]),a(j,{columns:L,"data-source":b.value,loading:C.value,pagination:{current:_.value,pageSize:m.value,total:V.value,onChange:B,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:F,showTotal:n=>`共 ${n} 条记录`},"row-key":n=>n.id},{bodyCell:t(({column:n,record:c})=>[n.key==="name"?(r(),p("a",{key:0,onClick:k=>s(g).push(`/config/${c.id}`)},$(c.name),9,se)):n.key==="description"?(r(),p("span",ie,$(c.description||"无描述"),1)):n.key==="created_at"?(r(),p("span",ce,$(s(te)(c.created_at)),1)):n.key==="action"?(r(),O(U,{key:3},{default:t(()=>[a(v,{type:"link",size:"small",onClick:k=>s(g).push(`/config/${c.id}`)},{icon:t(()=>[a(s(X))]),default:t(()=>[f(" 查看 ")]),_:2},1032,["onClick"]),a(v,{type:"link",size:"small",onClick:k=>s(g).push(`/config/${c.id}/edit`)},{icon:t(()=>[a(s(Y))]),default:t(()=>[f(" 编辑 ")]),_:2},1032,["onClick"]),a(v,{type:"link",size:"small",onClick:k=>T(c)},{icon:t(()=>[a(s(ne))]),default:t(()=>[f(" 配置 ")]),_:2},1032,["onClick"]),a(I,{title:"确定要删除此配置吗？","ok-text":"确定","cancel-text":"取消",onConfirm:k=>N(c.id)},{default:t(()=>[a(v,{type:"link",size:"small",danger:""},{icon:t(()=>[a(s(Z))]),default:t(()=>[f(" 删除 ")]),_:1})]),_:2},1032,["onConfirm"])]),_:2},1024)):E("",!0)]),_:1},8,["data-source","loading","pagination","row-key"]),a(q,{open:h.value,"onUpdate:open":e[3]||(e[3]=n=>h.value=n),title:`配置设置: ${((M=i.value)==null?void 0:M.name)||""}`,width:"800px",footer:null,onCancel:S},{default:t(()=>[i.value?(r(),p(ee,{key:0},[!i.value.conf_schema||Object.keys(i.value.conf_schema).length===0?(r(),p("div",ue,[a(R,{description:"没有可用的配置模式"})])):(r(),p("div",re,[a(oe,{schema:i.value.conf_schema,value:y.value,"onUpdate:value":e[2]||(e[2]=n=>y.value=n),loading:x.value,onSubmit:A,onCancel:S},null,8,["schema","value","loading"])]))],64)):E("",!0)]),_:1},8,["open","title"])]}),_:1})]),_:1})}}},ge=H(de,[["__scopeId","data-v-311f8493"]]);export{ge as default};
