import{e as a,A as _,U as q,q as x,_ as W,x as X,u as Y,V as G,r as M,y as Q,B as Z,b as p,c as y,d as K,w as o,h as c,k as D,f as u,W as F,O as tt,g as C,X as et,t as nt,D as at,Y as lt,p as rt,m as ot}from"./index-94aa741e.js";var ct={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M924.8 385.6a446.7 446.7 0 00-96-142.4 446.7 446.7 0 00-142.4-96C631.1 123.8 572.5 112 512 112s-119.1 11.8-174.4 35.2a446.7 446.7 0 00-142.4 96 446.7 446.7 0 00-96 142.4C75.8 440.9 64 499.5 64 560c0 132.7 58.3 257.7 159.9 343.1l1.7 1.4c5.8 4.8 13.1 7.5 20.6 7.5h531.7c7.5 0 14.8-2.7 20.6-7.5l1.7-1.4C901.7 817.7 960 692.7 960 560c0-60.5-11.9-119.1-35.2-174.4zM761.4 836H262.6A371.12 371.12 0 01140 560c0-99.4 38.7-192.8 109-263 70.3-70.3 163.7-109 263-109 99.4 0 192.8 38.7 263 109 70.3 70.3 109 163.7 109 263 0 105.6-44.5 205.5-122.6 276zM623.5 421.5a8.03 8.03 0 00-11.3 0L527.7 506c-18.7-5-39.4-.2-54.1 14.5a55.95 55.95 0 000 79.2 55.95 55.95 0 0079.2 0 55.87 55.87 0 0014.5-54.1l84.5-84.5c3.1-3.1 3.1-8.2 0-11.3l-28.3-28.3zM490 320h44c4.4 0 8-3.6 8-8v-80c0-4.4-3.6-8-8-8h-44c-4.4 0-8 3.6-8 8v80c0 4.4 3.6 8 8 8zm260 218v44c0 4.4 3.6 8 8 8h80c4.4 0 8-3.6 8-8v-44c0-4.4-3.6-8-8-8h-80c-4.4 0-8 3.6-8 8zm12.7-197.2l-31.1-31.1a8.03 8.03 0 00-11.3 0l-56.6 56.6a8.03 8.03 0 000 11.3l31.1 31.1c3.1 3.1 8.2 3.1 11.3 0l56.6-56.6c3.1-3.1 3.1-8.2 0-11.3zm-458.6-31.1a8.03 8.03 0 00-11.3 0l-31.1 31.1a8.03 8.03 0 000 11.3l56.6 56.6c3.1 3.1 8.2 3.1 11.3 0l31.1-31.1c3.1-3.1 3.1-8.2 0-11.3l-56.6-56.6zM262 530h-80c-4.4 0-8 3.6-8 8v44c0 4.4 3.6 8 8 8h80c4.4 0 8-3.6 8-8v-44c0-4.4-3.6-8-8-8z"}}]},name:"dashboard",theme:"outlined"};const st=ct;function H(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},l=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(l=l.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),l.forEach(function(r){ut(e,r,n[r])})}return e}function ut(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var S=function(t,n){var l=H({},t,n.attrs);return a(_,H({},l,{icon:st}),null)};S.displayName="DashboardOutlined";S.inheritAttrs=!1;const it=S;var dt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M688 312v-48c0-4.4-3.6-8-8-8H296c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8zm-392 88c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H296zm144 452H208V148h560v344c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V108c0-17.7-14.3-32-32-32H168c-17.7 0-32 14.3-32 32v784c0 17.7 14.3 32 32 32h272c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm445.7 51.5l-93.3-93.3C814.7 780.7 828 743.9 828 704c0-97.2-78.8-176-176-176s-176 78.8-176 176 78.8 176 176 176c35.8 0 69-10.7 96.8-29l94.7 94.7c1.6 1.6 3.6 2.3 5.6 2.3s4.1-.8 5.6-2.3l31-31a7.9 7.9 0 000-11.2zM652 816c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"}}]},name:"file-search",theme:"outlined"};const ft=dt;function k(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},l=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(l=l.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),l.forEach(function(r){pt(e,r,n[r])})}return e}function pt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var P=function(t,n){var l=k({},t,n.attrs);return a(_,k({},l,{icon:ft}),null)};P.displayName="FileSearchOutlined";P.inheritAttrs=!1;const B=P;var gt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M841 370c3-3.3 2.7-8.3-.6-11.3a8.24 8.24 0 00-5.3-2.1h-72.6c-2.4 0-4.6 1-6.1 2.8L633.5 504.6a7.96 7.96 0 01-13.4-1.9l-63.5-141.3a7.9 7.9 0 00-7.3-4.7H380.7l.9-4.7 8-42.3c10.5-55.4 38-81.4 85.8-81.4 18.6 0 35.5 1.7 48.8 4.7l14.1-66.8c-22.6-4.7-35.2-6.1-54.9-6.1-103.3 0-156.4 44.3-175.9 147.3l-9.4 49.4h-97.6c-3.8 0-7.1 2.7-7.8 6.4L181.9 415a8.07 8.07 0 007.8 9.7H284l-89 429.9a8.07 8.07 0 007.8 9.7H269c3.8 0 7.1-2.7 7.8-6.4l89.7-433.1h135.8l68.2 139.1c1.4 2.9 1 6.4-1.2 8.8l-180.6 203c-2.9 3.3-2.6 8.4.7 11.3 1.5 1.3 3.4 2 5.3 2h72.7c2.4 0 4.6-1 6.1-2.8l123.7-146.7c2.8-3.4 7.9-3.8 11.3-1 .9.8 1.6 1.7 2.1 2.8L676.4 784c1.3 2.8 4.1 4.7 7.3 4.7h64.6a8.02 8.02 0 007.2-11.5l-95.2-198.9c-1.4-2.9-.9-6.4 1.3-8.8L841 370z"}}]},name:"function",theme:"outlined"};const mt=gt;function N(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},l=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(l=l.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),l.forEach(function(r){ht(e,r,n[r])})}return e}function ht(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var w=function(t,n){var l=N({},t,n.attrs);return a(_,N({},l,{icon:mt}),null)};w.displayName="FunctionOutlined";w.inheritAttrs=!1;const _t=w;var Ot={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 732h-70.3c-4.8 0-9.3 2.1-12.3 5.8-7 8.5-14.5 16.7-22.4 24.5a353.84 353.84 0 01-112.7 75.9A352.8 352.8 0 01512.4 866c-47.9 0-94.3-9.4-137.9-27.8a353.84 353.84 0 01-112.7-75.9 353.28 353.28 0 01-76-112.5C167.3 606.2 158 559.9 158 512s9.4-94.2 27.8-137.8c17.8-42.1 43.4-80 76-112.5s70.5-58.1 112.7-75.9c43.6-18.4 90-27.8 137.9-27.8 47.9 0 94.3 9.3 137.9 27.8 42.2 17.8 80.1 43.4 112.7 75.9 7.9 7.9 15.3 16.1 22.4 24.5 3 3.7 7.6 5.8 12.3 5.8H868c6.3 0 10.2-7 6.7-12.3C798 160.5 663.8 81.6 511.3 82 271.7 82.6 79.6 277.1 82 516.4 84.4 751.9 276.2 942 512.4 942c152.1 0 285.7-78.8 362.3-197.7 3.4-5.3-.4-12.3-6.7-12.3zm88.9-226.3L815 393.7c-5.3-4.2-13-.4-13 6.3v76H488c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h314v76c0 6.7 7.8 10.5 13 6.3l141.9-112a8 8 0 000-12.6z"}}]},name:"logout",theme:"outlined"};const vt=Ot;function E(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},l=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(l=l.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),l.forEach(function(r){bt(e,r,n[r])})}return e}function bt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var j=function(t,n){var l=E({},t,n.attrs);return a(_,E({},l,{icon:vt}),null)};j.displayName="LogoutOutlined";j.inheritAttrs=!1;const yt=j;var St={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"}}]},name:"setting",theme:"outlined"};const Pt=St;function T(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},l=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(l=l.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),l.forEach(function(r){wt(e,r,n[r])})}return e}function wt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var $=function(t,n){var l=T({},t,n.attrs);return a(_,T({},l,{icon:Pt}),null)};$.displayName="SettingOutlined";$.inheritAttrs=!1;const jt=$;var $t={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M876.6 239.5c-.5-.9-1.2-1.8-2-2.5-5-5-13.1-5-18.1 0L684.2 409.3l-67.9-67.9L788.7 169c.8-.8 1.4-1.6 2-2.5 3.6-6.1 1.6-13.9-4.5-17.5-98.2-58-226.8-44.7-311.3 39.7-67 67-89.2 162-66.5 247.4l-293 293c-3 3-2.8 7.9.3 11l169.7 169.7c3.1 3.1 8.1 3.3 11 .3l292.9-292.9c85.5 22.8 180.5.7 247.6-66.4 84.4-84.5 97.7-213.1 39.7-311.3zM786 499.8c-58.1 58.1-145.3 69.3-214.6 33.6l-8.8 8.8-.1-.1-274 274.1-79.2-79.2 230.1-230.1s0 .1.1.1l52.8-52.8c-35.7-69.3-24.5-156.5 33.6-214.6a184.2 184.2 0 01144-53.5L537 318.9a32.05 32.05 0 000 45.3l124.5 124.5a32.05 32.05 0 0045.3 0l132.8-132.8c3.7 51.8-14.4 104.8-53.6 143.9z"}}]},name:"tool",theme:"outlined"};const Lt=$t;function V(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},l=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(l=l.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),l.forEach(function(r){zt(e,r,n[r])})}return e}function zt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var L=function(t,n){var l=V({},t,n.attrs);return a(_,V({},l,{icon:Lt}),null)};L.displayName="ToolOutlined";L.inheritAttrs=!1;const At=L,Wt=async e=>{const{method:t,url:n,data:l,params:r,successMessage:m,errorMessage:O,onSuccess:v}=e;try{const s=await q({method:t,url:n,data:l,params:r});return m&&x.success(m),v&&v(s.data.data,s.data),s.data}catch(s){throw!s.errorHandled&&!s.isApiError&&(x.error(O||"遇到错误："+(s.message||"未知错误")),s.errorHandled=!0),s}},Xt=e=>{try{return JSON.parse(e),{valid:!0}}catch(t){return{valid:!1,message:`JSON格式不正确: ${t.message}`}}},Yt=e=>e?new Date(e).toLocaleString():"",xt="/logo/logo.svg",Mt="/logo/logo-small.svg";const g=e=>(rt("data-v-e443fdeb"),e=e(),ot(),e),Dt={class:"logo"},Ft={key:0,src:xt,alt:"Easy MCP",class:"logo-large"},Ct={key:1,src:Mt,alt:"MCP",class:"logo-small"},Ht=g(()=>c("span",null,"仪表盘",-1)),kt=g(()=>c("span",null,"工具管理",-1)),Bt=g(()=>c("span",null,"函数管理",-1)),Nt=g(()=>c("span",null,"配置管理",-1)),Et=g(()=>c("span",null,"用户管理",-1)),Tt=g(()=>c("span",null,"工具日志",-1)),Vt=g(()=>c("span",null,"审计日志",-1)),It=g(()=>c("span",null,"系统日志",-1)),Ut={style:{"margin-right":"24px"}},Jt={class:"page-container",style:{padding:"16px"}},Rt={__name:"AppLayout",props:{currentPageKey:{type:String,default:"dashboard"}},setup(e){const t=e,n=X(),l=Y(),r=G(),m=M(!1),O=M([t.currentPageKey]),v=Q(()=>{var h;return((h=r.user)==null?void 0:h.username)||"未登录"});Z(()=>n.path,h=>{const d=h.split("/")[1]||"dashboard";O.value=[d]},{immediate:!0});const s=()=>{r.logout(),l.push("/login")};return(h,d)=>{const f=p("router-link"),i=p("a-menu-item"),z=p("a-menu"),I=p("a-layout-sider"),U=p("a-dropdown"),J=p("a-layout-header"),R=p("a-layout-content"),A=p("a-layout");return y(),K(A,{style:{"min-height":"100vh"}},{default:o(()=>[a(I,{collapsed:m.value,"onUpdate:collapsed":d[1]||(d[1]=b=>m.value=b),collapsible:"",width:180,"collapsed-width":64},{default:o(()=>[c("div",Dt,[m.value?(y(),D("img",Ct)):(y(),D("img",Ft))]),a(z,{selectedKeys:O.value,"onUpdate:selectedKeys":d[0]||(d[0]=b=>O.value=b),theme:"dark",mode:"inline"},{default:o(()=>[a(i,{key:"dashboard"},{icon:o(()=>[a(u(it))]),default:o(()=>[Ht,a(f,{to:"/dashboard"})]),_:1}),a(i,{key:"tool"},{icon:o(()=>[a(u(At))]),default:o(()=>[kt,a(f,{to:"/tool"})]),_:1}),a(i,{key:"func"},{icon:o(()=>[a(u(_t))]),default:o(()=>[Bt,a(f,{to:"/func"})]),_:1}),a(i,{key:"config"},{icon:o(()=>[a(u(jt))]),default:o(()=>[Nt,a(f,{to:"/config"})]),_:1}),a(i,{key:"user"},{icon:o(()=>[a(u(F))]),default:o(()=>[Et,a(f,{to:"/user"})]),_:1}),a(i,{key:"tool-log"},{icon:o(()=>[a(u(B))]),default:o(()=>[Tt,a(f,{to:"/tool-log"})]),_:1}),a(i,{key:"audit"},{icon:o(()=>[a(u(B))]),default:o(()=>[Vt,a(f,{to:"/audit"})]),_:1}),a(i,{key:"log"},{icon:o(()=>[a(u(tt))]),default:o(()=>[It,a(f,{to:"/log"})]),_:1})]),_:1},8,["selectedKeys"])]),_:1},8,["collapsed"]),a(A,null,{default:o(()=>[a(J,{style:{background:"#fff",padding:"0",display:"flex","justify-content":"flex-end",height:"48px","line-height":"48px"}},{default:o(()=>[c("div",Ut,[a(U,null,{overlay:o(()=>[a(z,null,{default:o(()=>[a(i,{key:"logout",onClick:s},{default:o(()=>[a(u(yt)),C(" 退出登录 ")]),_:1})]),_:1})]),default:o(()=>[c("a",{class:"ant-dropdown-link",onClick:d[2]||(d[2]=et(()=>{},["prevent"]))},[a(u(F)),C(" "+nt(v.value)+" ",1),a(u(at))])]),_:1})])]),_:1}),a(R,null,{default:o(()=>[c("div",Jt,[lt(h.$slots,"default",{},void 0,!0)])]),_:3})]),_:3})]),_:3})}}},Gt=W(Rt,[["__scopeId","data-v-e443fdeb"]]);export{Gt as A,_t as F,jt as S,At as T,Wt as c,Yt as f,Xt as v};
