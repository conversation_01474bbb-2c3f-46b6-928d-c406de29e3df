import{A as R,c as V}from"./AppLayout-b20be4c8.js";import{_ as U,r as l,B as $,o as D,b as c,c as s,d as h,w as i,e as t,h as e,f as L,R as S,g as b,N as B,k as _,l as T,F,z as j,t as d,O as q,p as G,m as H}from"./index-94aa741e.js";const J=r=>(G("data-v-abb00f56"),r=r(),H(),r),K={class:"log-container"},P={class:"log-files-panel"},Q={class:"panel-header"},W=J(()=>e("h3",null,"日志文件",-1)),X=["onClick"],Y={class:"log-file-info"},Z={class:"log-file-name"},ee={class:"log-file-meta"},ae={class:"log-content-panel"},se={key:0,class:"no-log-selected"},te={key:1,class:"log-content-container"},oe={class:"panel-header"},ne={class:"log-controls"},le={class:"log-info-bar"},ce={class:"log-content-viewer"},ie={key:1},de={__name:"LogListView",setup(r){const u=l(!1),f=l(!1),y=l([]),o=l(null),p=l({content:"",total_lines:0,displayed_lines:0}),v=l(1e3),m=l(!0),w=async()=>{u.value=!0;try{await V({method:"get",url:"/api/v1/log",onSuccess:n=>{y.value=n.files},errorMessage:"获取日志文件列表失败"})}finally{u.value=!1}},N=()=>{w()},I=n=>{o.value=n,k()},k=async()=>{if(o.value){f.value=!0;try{await V({method:"get",url:`/api/v1/log/content/${o.value.name}`,params:{max_lines:v.value,tail:m.value},onSuccess:n=>{p.value=n},errorMessage:"获取日志内容失败"})}catch(n){console.error("Error loading log content:",n)}finally{f.value=!1}}};return $([v,m],()=>{o.value&&k()}),D(()=>{w()}),(n,g)=>{const C=c("a-button"),x=c("a-spin"),z=c("a-empty"),M=c("a-input-number"),A=c("a-switch"),O=c("a-space"),E=c("a-card");return s(),h(R,{"current-page-key":"log"},{default:i(()=>[t(E,{title:"系统日志"},{default:i(()=>[e("div",K,[e("div",P,[e("div",Q,[W,t(C,{type:"primary",size:"small",onClick:N},{icon:i(()=>[t(L(S))]),default:i(()=>[b(" 刷新 ")]),_:1})]),e("div",{class:B(["log-files-list",{loading:u.value}])},[u.value?(s(),h(x,{key:0})):(s(),_(F,{key:1},[y.value.length===0?(s(),h(z,{key:0,description:"没有找到日志文件"})):T("",!0),(s(!0),_(F,null,j(y.value,a=>(s(),_("div",{key:a.name,class:B(["log-file-card",{active:o.value&&o.value.name===a.name}]),onClick:_e=>I(a)},[e("div",Y,[e("div",Z,[t(L(q)),b(" "+d(a.name),1)]),e("div",ee,[e("div",null,"大小: "+d(a.size_human),1),e("div",null,"修改时间: "+d(a.modified_at_human),1)])])],10,X))),128))],64))],2)]),e("div",ae,[o.value?(s(),_("div",te,[e("div",oe,[e("h3",null,d(o.value.name),1),e("div",ne,[t(O,null,{default:i(()=>[t(M,{value:v.value,"onUpdate:value":g[0]||(g[0]=a=>v.value=a),min:100,max:1e4,step:100,size:"small","addon-before":"行数"},null,8,["value"]),t(A,{checked:m.value,"onUpdate:checked":g[1]||(g[1]=a=>m.value=a),"checked-children":"末尾","un-checked-children":"开头",size:"small"},null,8,["checked"]),t(C,{type:"primary",size:"small",onClick:k},{icon:i(()=>[t(L(S))]),default:i(()=>[b(" 刷新 ")]),_:1})]),_:1})])]),e("div",le,[e("span",null,"总行数: "+d(p.value.total_lines),1),e("span",null,"显示行数: "+d(p.value.displayed_lines),1)]),e("div",ce,[f.value?(s(),h(x,{key:0})):(s(),_("pre",ie,d(p.value.content),1))])])):(s(),_("div",se,[t(z,{description:"请选择一个日志文件查看"})]))])])]),_:1})]),_:1})}}},pe=U(de,[["__scopeId","data-v-abb00f56"]]);export{pe as default};
