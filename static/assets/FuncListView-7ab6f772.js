import{u as T,r as u,o as A,b as c,c as i,d as C,w as e,e as n,h as D,f as s,P as I,g as d,k as _,t as k,F as L,E as R,s as U,v as j,l as q}from"./index-94aa741e.js";import{c as S,A as G,f as H}from"./AppLayout-b20be4c8.js";const J={class:"action-bar"},K=["onClick"],Q={key:1},W={key:1},X={key:3},te={__name:"FuncListView",setup(Y){const h=T(),y=u(!1),w=u([]),x=u(0),r=u(1),f=u(10),m=u(""),z=[{title:"名称",dataIndex:"name",key:"name",width:160,ellipsis:!0,tooltip:!0},{title:"描述",dataIndex:"description",key:"description",width:200,ellipsis:!0,tooltip:!0},{title:"版本",key:"version",width:100,ellipsis:!0,tooltip:!0},{title:"创建时间",key:"created_at",width:160,ellipsis:!0,tooltip:!0},{title:"操作",key:"action",width:200,fixed:"right"}];A(()=>{p()});const p=async()=>{y.value=!0;try{await S({method:"get",url:"/api/v1/func",params:{page:r.value,size:f.value,search:m.value||void 0},onSuccess:(l,t)=>{w.value=l,x.value=t.total},errorMessage:"获取函数列表失败"})}finally{y.value=!1}},$=()=>{r.value=1,p()},b=(l,t)=>{r.value=l,f.value=t,p()},E=(l,t)=>{r.value=1,f.value=t,p()},O=async l=>{try{await S({method:"delete",url:`/api/v1/func/${l}`,successMessage:"删除成功",errorMessage:"删除失败"}),p()}catch(t){console.error("Error deleting function:",t)}};return(l,t)=>{const P=c("a-input-search"),v=c("a-button"),V=c("a-tag"),B=c("a-popconfirm"),F=c("a-space"),M=c("a-table"),N=c("a-card");return i(),C(G,{"current-page-key":"func"},{default:e(()=>[n(N,{title:"函数管理"},{default:e(()=>[D("div",J,[n(P,{value:m.value,"onUpdate:value":t[0]||(t[0]=a=>m.value=a),placeholder:"搜索函数名称或描述",style:{width:"300px"},onSearch:$},null,8,["value"]),n(v,{type:"primary",onClick:t[1]||(t[1]=a=>s(h).push("/func/create"))},{icon:e(()=>[n(s(I))]),default:e(()=>[d(" 创建函数 ")]),_:1})]),n(M,{columns:z,"data-source":w.value,loading:y.value,pagination:{current:r.value,pageSize:f.value,total:x.value,onChange:b,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:E,showTotal:a=>`共 ${a} 条记录`},"row-key":a=>a.id},{bodyCell:e(({column:a,record:o})=>[a.key==="name"?(i(),_("a",{key:0,onClick:g=>s(h).push(`/func/${o.id}`)},k(o.name),9,K)):a.key==="description"?(i(),_("span",Q,k(o.description||"无描述"),1)):a.key==="version"?(i(),_(L,{key:2},[o.current_version?(i(),C(V,{key:0,color:"blue"},{default:e(()=>[d("v"+k(o.current_version),1)]),_:2},1024)):(i(),_("span",W,"未发布"))],64)):a.key==="created_at"?(i(),_("span",X,k(s(H)(o.created_at)),1)):a.key==="action"?(i(),C(F,{key:4},{default:e(()=>[n(v,{type:"link",size:"small",onClick:g=>s(h).push(`/func/${o.id}`)},{icon:e(()=>[n(s(R))]),default:e(()=>[d(" 查看 ")]),_:2},1032,["onClick"]),n(v,{type:"link",size:"small",onClick:g=>s(h).push(`/func/${o.id}/edit`)},{icon:e(()=>[n(s(U))]),default:e(()=>[d(" 编辑 ")]),_:2},1032,["onClick"]),n(B,{title:"确定要删除此函数吗？","ok-text":"确定","cancel-text":"取消",onConfirm:g=>O(o.id)},{default:e(()=>[n(v,{type:"link",size:"small",danger:""},{icon:e(()=>[n(s(j))]),default:e(()=>[d(" 删除 ")]),_:1})]),_:2},1032,["onConfirm"])]),_:2},1024)):q("",!0)]),_:1},8,["data-source","loading","pagination","row-key"])]),_:1})]),_:1})}}};export{te as default};
