import{e as n,A as H,_ as Q,u as F,x as G,y as v,r as y,a as W,o as X,b as i,c as O,d as N,w as r,f as g,g as J,h as m,l as M,q as _}from"./index-94aa741e.js";import{c as w,A as Y,v as T}from"./AppLayout-b20be4c8.js";import{_ as Z}from"./MonacoEditor-7a57fdbb.js";import{J as ee}from"./JsonSchemaEditor-0d8e4465.js";import{Q as te}from"./QuestionCircleOutlined-0a580e36.js";import{R as ae}from"./RollbackOutlined-27c22304.js";import{S as ne}from"./SaveOutlined-9c7136e5.js";/* empty css                                                     */var re={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M848 359.3H627.7L825.8 109c4.1-5.3.4-13-6.3-13H436c-2.8 0-5.5 1.5-6.9 4L170 547.5c-3.1 5.3.7 12 6.9 12h174.4l-89.4 357.6c-1.9 7.8 7.5 13.3 13.3 7.7L853.5 373c5.2-4.9 1.7-13.7-5.5-13.7zM378.2 732.5l60.3-241H281.1l189.6-327.4h224.6L487 427.4h211L378.2 732.5z"}}]},name:"thunderbolt",theme:"outlined"};const se=re;function A(c){for(var s=1;s<arguments.length;s++){var o=arguments[s]!=null?Object(arguments[s]):{},u=Object.keys(o);typeof Object.getOwnPropertySymbols=="function"&&(u=u.concat(Object.getOwnPropertySymbols(o).filter(function(p){return Object.getOwnPropertyDescriptor(o,p).enumerable}))),u.forEach(function(p){oe(c,p,o[p])})}return c}function oe(c,s,o){return s in c?Object.defineProperty(c,s,{value:o,enumerable:!0,configurable:!0,writable:!0}):c[s]=o,c}var x=function(s,o){var u=A({},s,o.attrs);return n(H,A({},u,{icon:se}),null)};x.displayName="ThunderboltOutlined";x.inheritAttrs=!1;const le=x;const ie={class:"tab-description"},ce={class:"schema-editor-container"},ue={class:"tab-description"},de={class:"generate-button-container"},pe={class:"editor-container"},fe={__name:"ConfigEditView",setup(c){const s=F(),o=G(),u=v(()=>o.params.id),p=v(()=>!!u.value),S=y(!1),C=y(!1),V=y("schema"),t=W({name:"",description:"",schemaStr:JSON.stringify({type:"object",properties:{apiKey:{type:"string",description:"API密钥"},timeout:{type:"integer",description:"超时时间（秒）",default:30}},required:["apiKey"]},null,2),valuesStr:JSON.stringify({apiKey:"",timeout:30},null,2)});v(()=>{try{const e=JSON.parse(t.schemaStr);return JSON.stringify(e,null,2)}catch{return"无效的 JSON"}}),v(()=>{try{const e=JSON.parse(t.valuesStr);return JSON.stringify(e,null,2)}catch{return"无效的 JSON"}});const b=v(()=>{try{const e=JSON.parse(t.schemaStr);return e&&e.type==="object"&&e.properties&&Object.keys(e.properties).length>0}catch{return!1}}),E=()=>{try{const e=JSON.parse(t.schemaStr);if(!e||e.type!=="object"||!e.properties){_.error("配置定义不是有效的JSON Schema");return}const a={};Object.entries(e.properties).forEach(([l,f])=>{if(f.default!==void 0)a[l]=f.default;else switch(f.type){case"string":a[l]="";break;case"number":case"integer":a[l]=0;break;case"boolean":a[l]=!1;break;case"array":a[l]=[];break;case"object":a[l]={};break;default:a[l]=null}}),t.valuesStr=JSON.stringify(a,null,2),_.success("配置信息生成成功")}catch(e){_.error(`生成配置信息失败: ${e.message}`)}};X(()=>{p.value&&B()});const B=async()=>{C.value=!0;try{await w({method:"get",url:`/api/v1/config/${u.value}`,onSuccess:e=>{t.name=e.name,t.description=e.description||"",t.schemaStr=JSON.stringify(e.conf_schema,null,2),t.valuesStr=e.conf_value?JSON.stringify(e.conf_value,null,2):"{}"},errorMessage:"获取配置数据失败"})}finally{C.value=!1}},P=()=>{if(!t.name)return _.error("请输入配置名称"),!1;const e=T(t.schemaStr);if(!e.valid)return _.error("配置架构 JSON 格式不正确: "+e.message),!1;if(t.valuesStr){const a=T(t.valuesStr);if(!a.valid)return _.error("配置值 JSON 格式不正确: "+a.message),!1}return!0},$=()=>({name:t.name,description:t.description,conf_schema:JSON.parse(t.schemaStr),conf_value:t.valuesStr?JSON.parse(t.valuesStr):null}),z=async()=>{if(P()){S.value=!0;try{const e=$();p.value?await w({method:"put",url:`/api/v1/config/${u.value}`,data:e,successMessage:"保存成功",errorMessage:"保存失败",onSuccess:()=>{h()}}):await w({method:"post",url:"/api/v1/config",data:e,successMessage:"创建成功",errorMessage:"创建失败",onSuccess:()=>{h()}})}finally{S.value=!1}}},h=()=>{s.back()};return(e,a)=>{const l=i("a-button"),f=i("a-space"),K=i("a-input"),j=i("a-form-item"),U=i("a-textarea"),k=i("a-alert"),L=i("a-tab-pane"),I=i("a-tooltip"),R=i("a-tabs"),q=i("a-form"),D=i("a-card");return O(),N(Y,{"current-page-key":"config"},{default:r(()=>[n(D,{title:p.value?"编辑配置":"创建配置"},{extra:r(()=>[n(f,null,{default:r(()=>[n(l,{size:"middle",onClick:h},{icon:r(()=>[n(g(ae))]),default:r(()=>[J(" 返回 ")]),_:1}),n(l,{type:"primary",loading:S.value,size:"middle",onClick:z},{icon:r(()=>[n(g(ne))]),default:r(()=>[J(" 保存 ")]),_:1},8,["loading"])]),_:1})]),default:r(()=>[n(q,{model:t,layout:"vertical",class:"form-container"},{default:r(()=>[n(j,{label:"配置名称",required:""},{default:r(()=>[n(K,{value:t.name,"onUpdate:value":a[0]||(a[0]=d=>t.name=d),placeholder:"请输入配置名称"},null,8,["value"])]),_:1}),n(j,{label:"配置描述"},{default:r(()=>[n(U,{value:t.description,"onUpdate:value":a[1]||(a[1]=d=>t.description=d),placeholder:"请输入配置描述",rows:3},null,8,["value"])]),_:1}),m("div",null,[n(R,{activeKey:V.value,"onUpdate:activeKey":a[4]||(a[4]=d=>V.value=d)},{default:r(()=>[n(L,{key:"schema",tab:"配置定义","force-render":""},{default:r(()=>[m("div",ie,[n(k,{message:"配置定义说明",description:"定义配置项的结构。您可以使用图形化编辑器或直接编辑JSON Schema。",type:"info","show-icon":""})]),m("div",ce,[n(ee,{value:t.schemaStr,"onUpdate:value":a[2]||(a[2]=d=>t.schemaStr=d)},null,8,["value"])])]),_:1}),n(L,{key:"values",tab:"配置设置","force-render":""},{default:r(()=>[m("div",ue,[m("div",de,[n(f,null,{default:r(()=>[n(l,{type:"primary",disabled:!b.value,onClick:E,size:"middle"},{icon:r(()=>[n(g(le))]),default:r(()=>[J(" 生成配置信息 ")]),_:1},8,["disabled"]),b.value?M("",!0):(O(),N(I,{key:0,title:"配置定义不是有效的JSON Schema"},{default:r(()=>[n(g(te),{class:"tooltip-icon"})]),_:1}))]),_:1}),b.value?(O(),N(k,{key:0,message:"点击生成配置信息按钮，系统将根据配置定义自动生成配置值",type:"info","show-icon":"",style:{"margin-top":"8px"}})):M("",!0)])]),m("div",pe,[n(Z,{value:t.valuesStr,"onUpdate:value":a[3]||(a[3]=d=>t.valuesStr=d),language:"json",options:{automaticLayout:!0,scrollBeyondLastLine:!1}},null,8,["value"])])]),_:1})]),_:1},8,["activeKey"])])]),_:1},8,["model"])]),_:1},8,["title"])]),_:1})}}},Oe=Q(fe,[["__scopeId","data-v-da5eb708"]]);export{Oe as default};
