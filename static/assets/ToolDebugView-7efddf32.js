import{_ as ce,u as ie,x as de,y as K,r as S,a as I,o as _e,B as ve,b as r,c as n,d as c,w as o,e as i,f as $,g as h,k as p,h as C,F as O,z as J,R as me,l as T,t as j,q as b,p as pe,m as fe}from"./index-94aa741e.js";import{c as M,A as ge}from"./AppLayout-b20be4c8.js";import{R as he}from"./RollbackOutlined-27c22304.js";import{P as be}from"./PlayCircleOutlined-5272f431.js";const z=w=>(pe("data-v-28554c6a"),w=w(),fe(),w),Ue={key:0,class:"debug-container"},ye={class:"debug-form"},Se=z(()=>C("h3",null,"参数设置",-1)),Oe={class:"debug-result"},we=z(()=>C("h3",null,"执行结果",-1)),xe={key:0,class:"result-container"},Ne={key:0,class:"result-content"},$e={__name:"ToolDebugView",setup(w){const L=ie(),P=de(),V=K(()=>P.params.id),x=S(!1),_=S(null),N=S(!1),m=S(null),U=S("result"),s=I({}),f=I({}),g=I({}),d=K(()=>!_.value||!_.value.parameters?null:_.value.parameters),G=e=>!d.value||!d.value.required?!1:d.value.required.includes(e);_e(()=>{X()}),ve(()=>_.value,e=>{e&&e.parameters&&e.parameters.properties&&R()},{immediate:!0});const R=()=>{!d.value||!d.value.properties||(Object.keys(s).forEach(e=>delete s[e]),Object.keys(f).forEach(e=>delete f[e]),Object.keys(g).forEach(e=>delete g[e]),Object.entries(d.value.properties).forEach(([e,u])=>{if(u.default!==void 0)s[e]=u.default;else switch(u.type){case"string":s[e]="";break;case"number":case"integer":s[e]=null;break;case"boolean":s[e]=!1;break;case"array":s[e]=[],f[e]="[]";break;case"object":s[e]={},g[e]="{}";break;default:s[e]=null}}))},H=()=>{R(),b.success("表单已重置")},Q=e=>{try{const u=JSON.parse(f[e]);Array.isArray(u)?s[e]=u:b.error("请输入有效的JSON数组")}catch(u){b.error("JSON格式错误: "+u.message)}},W=e=>{try{const u=JSON.parse(g[e]);u&&typeof u=="object"&&!Array.isArray(u)?s[e]=u:b.error("请输入有效的JSON对象")}catch(u){b.error("JSON格式错误: "+u.message)}},X=async()=>{x.value=!0;try{await M({method:"get",url:`/api/v1/tool/${V.value}`,onSuccess:e=>{_.value=e},errorMessage:"获取工具数据失败"})}catch(e){console.error("Error fetching tool data:",e)}finally{x.value=!1}},Y=async()=>{var e,u;N.value=!0,m.value=null;try{await M({method:"post",url:`/api/v1/tool/${V.value}/debug`,data:{parameters:s},onSuccess:v=>{m.value=v,v.success?(U.value="result",b.success("执行成功")):U.value="logs"},errorMessage:null})}catch(v){console.error("Error debugging tool:",v),m.value={success:!1,error:((u=(e=v.response)==null?void 0:e.data)==null?void 0:u.message)||v.message||"调试请求失败"},U.value="logs"}finally{N.value=!1}},Z=e=>{try{return typeof e=="object"?JSON.stringify(e,null,2):String(e)}catch{return String(e)}};return(e,u)=>{const v=r("a-button"),ee=r("a-space"),A=r("a-select-option"),E=r("a-select"),k=r("a-textarea"),q=r("a-input"),ae=r("a-input-number"),te=r("a-switch"),D=r("a-form-item"),y=r("a-empty"),le=r("a-form"),se=r("a-alert"),B=r("a-tab-pane"),ne=r("a-list-item"),oe=r("a-list"),ue=r("a-tabs"),F=r("a-spin"),re=r("a-card");return n(),c(ge,{"current-page-key":"tool"},{default:o(()=>[i(re,{title:`调试工具: ${_.value?_.value.name:"加载中..."}`},{extra:o(()=>[i(ee,null,{default:o(()=>[i(v,{onClick:u[0]||(u[0]=t=>$(L).push(`/tool/${V.value}`))},{icon:o(()=>[i($(he))]),default:o(()=>[h(" 返回 ")]),_:1})]),_:1})]),default:o(()=>[i(F,{spinning:x.value},{default:o(()=>[_.value?(n(),p("div",Ue,[C("div",ye,[Se,i(le,{layout:"vertical"},{default:o(()=>[d.value&&d.value.properties?(n(!0),p(O,{key:0},J(d.value.properties,(t,a)=>(n(),c(D,{key:a,label:t.title||a,required:G(a),help:t.description},{default:o(()=>[t.type==="string"?(n(),p(O,{key:0},[t.enum?(n(),c(E,{key:0,value:s[a],"onUpdate:value":l=>s[a]=l,placeholder:`请选择${t.title||a}`},{default:o(()=>[(n(!0),p(O,null,J(t.enum,l=>(n(),c(A,{key:l,value:l},{default:o(()=>[h(j(l),1)]),_:2},1032,["value"]))),128))]),_:2},1032,["value","onUpdate:value","placeholder"])):t.format==="textarea"?(n(),c(k,{key:1,value:s[a],"onUpdate:value":l=>s[a]=l,rows:4,placeholder:`请输入${t.title||a}`},null,8,["value","onUpdate:value","placeholder"])):(n(),c(q,{key:2,value:s[a],"onUpdate:value":l=>s[a]=l,placeholder:`请输入${t.title||a}`},null,8,["value","onUpdate:value","placeholder"]))],64)):t.type==="number"||t.type==="integer"?(n(),c(ae,{key:1,value:s[a],"onUpdate:value":l=>s[a]=l,min:t.minimum,max:t.maximum,step:t.type==="integer"?1:.1,style:{width:"100%"}},null,8,["value","onUpdate:value","min","max","step"])):t.type==="boolean"?(n(),c(te,{key:2,checked:s[a],"onUpdate:checked":l=>s[a]=l},null,8,["checked","onUpdate:checked"])):t.type==="array"?(n(),p(O,{key:3},[t.items&&t.items.enum?(n(),c(E,{key:0,value:s[a],"onUpdate:value":l=>s[a]=l,mode:"multiple",placeholder:`请选择${t.title||a}`},{default:o(()=>[(n(!0),p(O,null,J(t.items.enum,l=>(n(),c(A,{key:l,value:l},{default:o(()=>[h(j(l),1)]),_:2},1032,["value"]))),128))]),_:2},1032,["value","onUpdate:value","placeholder"])):(n(),c(k,{key:1,value:f[a],"onUpdate:value":l=>f[a]=l,rows:3,placeholder:"请输入JSON数组，例如: ['item1', 'item2']",onChange:l=>Q(a)},null,8,["value","onUpdate:value","onChange"]))],64)):t.type==="object"?(n(),c(k,{key:4,value:g[a],"onUpdate:value":l=>g[a]=l,rows:4,placeholder:"请输入JSON对象，例如: {'key': 'value'}",onChange:l=>W(a)},null,8,["value","onUpdate:value","onChange"])):(n(),c(q,{key:5,value:s[a],"onUpdate:value":l=>s[a]=l,placeholder:`请输入${t.title||a}`},null,8,["value","onUpdate:value","placeholder"]))]),_:2},1032,["label","required","help"]))),128)):(n(),c(y,{key:1,description:"无参数定义或参数格式错误"})),i(D,null,{default:o(()=>[i(v,{type:"primary",loading:N.value,disabled:!_.value||!d.value,onClick:Y},{icon:o(()=>[i($(be))]),default:o(()=>[h(" 执行 ")]),_:1},8,["loading","disabled"]),i(v,{style:{"margin-left":"8px"},disabled:!_.value||!d.value,onClick:H},{icon:o(()=>[i($(me))]),default:o(()=>[h(" 重置 ")]),_:1},8,["disabled"])]),_:1})]),_:1})]),C("div",Oe,[we,i(F,{spinning:N.value},{default:o(()=>[m.value?(n(),p("div",xe,[m.value.success?T("",!0):(n(),c(se,{key:0,type:"error","show-icon":"",message:"执行失败",description:m.value.error_message,class:"error-alert"},null,8,["description"])),i(ue,{activeKey:U.value,"onUpdate:activeKey":u[1]||(u[1]=t=>U.value=t)},{default:o(()=>[i(B,{key:"result",tab:"返回值"},{default:o(()=>[m.value.success?(n(),p("pre",Ne,j(Z(m.value.result)),1)):(n(),c(y,{key:1,description:"执行失败，无返回值"}))]),_:1}),i(B,{key:"logs",tab:"日志"},{default:o(()=>[i(oe,{class:"log-list","data-source":m.value.logs,size:"small",bordered:""},{renderItem:o(({item:t})=>[i(ne,null,{default:o(()=>[h(j(t),1)]),_:2},1024)]),empty:o(()=>[i(y,{description:"无日志"})]),_:1},8,["data-source"])]),_:1})]),_:1},8,["activeKey"])])):(n(),c(y,{key:1,description:"暂无执行结果"}))]),_:1},8,["spinning"])])])):x.value?T("",!0):(n(),c(y,{key:1,description:"工具不存在或无法访问"}))]),_:1},8,["spinning"])]),_:1},8,["title"])]),_:1})}}},Ie=ce($e,[["__scopeId","data-v-28554c6a"]]);export{Ie as default};
