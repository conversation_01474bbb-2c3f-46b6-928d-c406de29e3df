import{_ as j,u as G,x as H,y as C,r as v,a as J,o as K,B as h,b as r,c as N,d as Q,w as o,e as n,f as w,g as x,t as W,h as k,q as E}from"./index-94aa741e.js";import{c as i,A as X}from"./AppLayout-b20be4c8.js";import{_ as Y}from"./MonacoEditor-7a57fdbb.js";import{R as ee}from"./RollbackOutlined-27c22304.js";import{S as ae}from"./SaveOutlined-9c7136e5.js";import{C as te}from"./CloudUploadOutlined-ca8531ec.js";/* empty css                                                     */const ne={class:"tab-description"},oe={class:"editor-container"},se={__name:"FuncEditView",setup(re){const A=G(),R=H(),d=C(()=>R.params.id),p=C(()=>!!d.value),f=v(!1),b=v(!1),g=v(!1),a=J({name:"",description:"",code:`# 函数实现代码

def my_function(param1, param2):
    """函数文档
    
    Args:
        param1: 参数1
        param2: 参数2
        
    Returns:
        返回值
    """
    return param1 + param2
`,depend_ids:[]}),S=v([]);K(async()=>{await B(),p.value&&await V()});const B=async()=>{g.value=!0;try{await i({method:"get",url:"/api/v1/func",params:{page:1,size:100},onSuccess:e=>{const t=p.value?e.filter(s=>s.id!==parseInt(d.value)):e;S.value=t.map(s=>({value:s.id,label:s.name}))},errorMessage:"获取函数列表失败"})}finally{g.value=!1}},V=async()=>{b.value=!0;try{await i({method:"get",url:`/api/v1/func/${d.value}`,onSuccess:e=>{a.code=e.code,c=M(e.code)||"",a.name=e.name,a.description=e.description||"",D()},errorMessage:"获取函数数据失败"})}finally{b.value=!1}},D=async()=>{try{await i({method:"get",url:`/api/v1/func/${d.value}/depend`,onSuccess:e=>{e&&Array.isArray(e)?a.depend_ids=e.map(t=>t.id):a.depend_ids=[]},errorMessage:"获取函数依赖关系失败"})}catch(e){console.error("Error fetching function dependencies:",e)}},$=()=>a.name?a.code?!0:(E.error("请输入函数代码"),!1):(E.error("请输入函数名称"),!1),F=()=>({name:a.name,description:a.description,code:a.code,depend_ids:a.depend_ids}),L=async()=>{if($()){f.value=!0;try{const e=F();p.value?await i({method:"put",url:`/api/v1/func/${d.value}`,data:e,successMessage:"保存成功",errorMessage:"保存失败",onSuccess:()=>{m()}}):await i({method:"post",url:"/api/v1/func",data:e,successMessage:"创建成功",errorMessage:"创建失败",onSuccess:()=>{m()}})}finally{f.value=!1}}},O=async()=>{if($()){f.value=!0;try{const e=F();p.value?await i({method:"put",url:`/api/v1/func/${d.value}/deploy`,data:e,successMessage:"保存并发布成功",errorMessage:"保存并发布失败",onSuccess:()=>{m()}}):await i({method:"post",url:"/api/v1/func/deploy",data:e,successMessage:"创建并发布成功",errorMessage:"创建并发布失败",onSuccess:()=>{m()}})}finally{f.value=!1}}},M=e=>{const t=e.match(/def\s+([a-zA-Z0-9_]+)\s*\(/);return t?t[1]:null},U=(e,t)=>{if(!e||!t||e===t)return a.code;try{const s=e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");let l=a.code.replace(new RegExp(`def\\s+${s}\\s*\\(`,"g"),`def ${t}(`);return l=l.replace(new RegExp(`\\b${s}\\s*\\(`,"g"),`${t}(`),l.includes('"""')&&(l=l.replace(new RegExp(`"""\\s*${s}\\b`,"g"),`"""${t}`)),l}catch(s){return console.error("Error updating function name in code:",s),a.code}};let y=!0,c="my_function";h(()=>a.code,e=>{(y||!c)&&(c=M(e)||"my_function",y=!1)},{immediate:!0}),h(()=>a.code,e=>{!a.name&&c&&c!=="function_name"&&(a.name=c)},{immediate:!0}),h(()=>a.name,(e,t)=>{y||e&&(a.code=U(c,e),c=e)});const m=()=>{A.back()};return(e,t)=>{const s=r("a-button"),l=r("a-space"),I=r("a-input"),_=r("a-form-item"),q=r("a-textarea"),z=r("a-select"),P=r("a-alert"),T=r("a-form"),Z=r("a-card");return N(),Q(X,{"current-page-key":"func"},{default:o(()=>[n(Z,{title:p.value?"编辑函数":"创建函数"},{extra:o(()=>[n(l,null,{default:o(()=>[n(s,{onClick:m},{icon:o(()=>[n(w(ee))]),default:o(()=>[x(" 返回 ")]),_:1}),n(s,{type:"primary",loading:f.value,onClick:L},{icon:o(()=>[n(w(ae))]),default:o(()=>[x(" 保存 ")]),_:1},8,["loading"]),n(s,{type:"primary",loading:f.value,onClick:O},{icon:o(()=>[n(w(te))]),default:o(()=>[x(" "+W(p.value?"保存并发布":"创建并发布"),1)]),_:1},8,["loading"])]),_:1})]),default:o(()=>[n(T,{model:a,layout:"vertical",class:"form-container"},{default:o(()=>[n(_,{label:"函数名称",required:""},{default:o(()=>[n(I,{value:a.name,"onUpdate:value":t[0]||(t[0]=u=>a.name=u),placeholder:"请输入函数名称"},null,8,["value"])]),_:1}),n(_,{label:"函数描述"},{default:o(()=>[n(q,{value:a.description,"onUpdate:value":t[1]||(t[1]=u=>a.description=u),placeholder:"请输入函数描述",rows:3},null,8,["value"])]),_:1}),n(_,{label:"依赖函数"},{default:o(()=>[n(z,{value:a.depend_ids,"onUpdate:value":t[2]||(t[2]=u=>a.depend_ids=u),mode:"multiple",placeholder:"请选择依赖函数",loading:g.value,options:S.value},null,8,["value","loading","options"])]),_:1}),n(_,{label:"函数代码",required:""},{default:o(()=>[k("div",ne,[n(P,{message:"函数代码说明",description:"编写Python函数代码。函数名称应与上方输入的函数名称保持一致（修改函数名称时会自动调整）。函数可以被其他函数或工具调用，请确保函数签名和文档注释清晰。",type:"info","show-icon":""})]),k("div",oe,[n(Y,{value:a.code,"onUpdate:value":t[3]||(t[3]=u=>a.code=u),language:"python",options:{automaticLayout:!0,scrollBeyondLastLine:!1}},null,8,["value"])])]),_:1})]),_:1},8,["model"])]),_:1},8,["title"])]),_:1})}}},me=j(se,[["__scopeId","data-v-188f91af"]]);export{me as default};
