import{u as j,x as q,y as F,r as c,o as G,b as u,c as _,d as v,w as o,e as l,f as r,g as f,s as H,h as e,t as i,k as J,l as T}from"./index-94aa741e.js";import{c as $,A as Q,f as m}from"./AppLayout-b20be4c8.js";import{R as W}from"./RollbackOutlined-27c22304.js";const X={class:"info-container"},Y={class:"info-row"},Z=e("div",{class:"info-label"},"用户名:",-1),ee={class:"info-value"},te={class:"info-row"},ae=e("div",{class:"info-label"},"邮箱:",-1),oe={class:"info-value"},se={class:"info-row"},ne=e("div",{class:"info-label"},"创建时间:",-1),le={class:"info-value"},ie={class:"info-row"},ce=e("div",{class:"info-label"},"更新时间:",-1),ue={class:"info-value"},re={class:"info-row"},de=e("div",{class:"info-label"},"创建者:",-1),_e={class:"info-value"},pe={class:"info-row"},ve=e("div",{class:"info-label"},"更新者:",-1),fe={class:"info-value"},ye={key:2},ke={__name:"UserDetailView",setup(ge){const b=j(),z=q(),k=F(()=>z.params.id),w=c(!1),s=c(null),C=c("basic"),M=c([]),y=c(!1),g=c(1),h=c(10),x=c(0),V=[{title:"操作类型",key:"action",width:100},{title:"资源类型",key:"resource_type",width:100},{title:"资源ID",key:"resource_id",width:80},{title:"资源名称",key:"resource_name",width:150,ellipsis:!0,tooltip:!0},{title:"IP地址",dataIndex:"ip_address",key:"ip_address",width:120},{title:"操作时间",key:"created_at",width:180}];G(()=>{B(),A()});const B=async()=>{w.value=!0;try{await $({method:"get",url:`/api/v1/user/${k.value}`,onSuccess:t=>{s.value=t},errorMessage:"获取用户数据失败"})}finally{w.value=!1}},A=async()=>{var t;y.value=!0;try{await $({method:"get",url:"/api/v1/audit",params:{page:g.value,size:h.value,username:(t=s.value)==null?void 0:t.username},onSuccess:(a,p)=>{M.value=a,x.value=p.total},errorMessage:"获取审计日志失败"})}finally{y.value=!1}},D=(t,a)=>{g.value=t,h.value=a,A()},I=t=>({create:"创建",update:"更新",delete:"删除",deploy:"发布",rollback:"回滚"})[t]||t,K=t=>({create:"green",update:"blue",delete:"red",deploy:"purple",rollback:"orange"})[t]||"default",N=t=>({user:"用户",tool:"工具",func:"函数",config:"配置"})[t]||t,P=t=>({user:"blue",tool:"purple",func:"green",config:"orange"})[t]||"default";return(t,a)=>{const p=u("a-button"),L=u("a-space"),S=u("a-tab-pane"),R=u("a-tag"),O=u("a-table"),U=u("a-tabs"),E=u("a-card");return _(),v(Q,{"current-page-key":"user"},{default:o(()=>[s.value?(_(),v(E,{key:0,title:`用户详情 - ${s.value?s.value.username:""}`},{extra:o(()=>[l(L,null,{default:o(()=>[l(p,{onClick:a[0]||(a[0]=n=>r(b).push("/user"))},{icon:o(()=>[l(r(W))]),default:o(()=>[f(" 返回 ")]),_:1}),l(p,{type:"primary",onClick:a[1]||(a[1]=n=>r(b).push(`/user/${k.value}/edit`))},{icon:o(()=>[l(r(H))]),default:o(()=>[f(" 编辑 ")]),_:1})]),_:1})]),default:o(()=>[l(U,{activeKey:C.value,"onUpdate:activeKey":a[2]||(a[2]=n=>C.value=n)},{default:o(()=>[l(S,{key:"basic",tab:"基本信息"},{default:o(()=>[e("div",X,[e("div",Y,[Z,e("div",ee,i(s.value.username),1)]),e("div",te,[ae,e("div",oe,i(s.value.email),1)]),e("div",se,[ne,e("div",le,i(r(m)(s.value.created_at)),1)]),e("div",ie,[ce,e("div",ue,i(r(m)(s.value.updated_at)),1)]),e("div",re,[de,e("div",_e,i(s.value.created_by||"-"),1)]),e("div",pe,[ve,e("div",fe,i(s.value.updated_by||"-"),1)])])]),_:1}),l(S,{key:"history",tab:"操作历史"},{default:o(()=>[l(O,{columns:V,"data-source":M.value,loading:y.value,pagination:{current:g.value,pageSize:h.value,total:x.value,onChange:D,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],showTotal:n=>`共 ${n} 条记录`},"row-key":n=>n.id},{bodyCell:o(({column:n,record:d})=>[n.key==="action"?(_(),v(R,{key:0,color:K(d.action)},{default:o(()=>[f(i(I(d.action)),1)]),_:2},1032,["color"])):n.key==="resource_type"?(_(),v(R,{key:1,color:P(d.resource_type)},{default:o(()=>[f(i(N(d.resource_type)),1)]),_:2},1032,["color"])):n.key==="created_at"?(_(),J("span",ye,i(r(m)(d.created_at)),1)):T("",!0)]),_:1},8,["data-source","loading","pagination","row-key"])]),_:1})]),_:1},8,["activeKey"])]),_:1},8,["title"])):T("",!0)]),_:1})}}};export{ke as default};
