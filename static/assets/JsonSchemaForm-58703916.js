import{r as E,y as H,B as x,o as J,b as m,c as p,k as v,e as c,w as u,F as g,z as Y,d as h,g as q,t as V}from"./index-94aa741e.js";const z={class:"json-schema-form"},T={__name:"JsonSchemaForm",props:{schema:{type:Object,required:!0},value:{type:Object,default:()=>({})},loading:{type:Boolean,default:!1}},emits:["update:value","submit","cancel"],setup(U,{emit:_}){const r=U,t=E({}),D=H(()=>{const o={};return r.schema&&r.schema.properties&&Object.keys(r.schema.properties).forEach(n=>{const s=r.schema.properties[n];o[n]={...s,required:r.schema.required&&r.schema.required.includes(n)}}),o}),d=()=>{const o={...r.value};r.schema&&r.schema.properties&&Object.keys(r.schema.properties).forEach(n=>{const s=r.schema.properties[n];o[n]===void 0&&s.default!==void 0&&(o[n]=s.default),o[n]===void 0&&(s.type==="string"?o[n]="":s.type==="number"||s.type==="integer"?o[n]=null:s.type==="boolean"?o[n]=!1:s.type==="array"?o[n]=[]:s.type==="object"&&(o[n]={}))}),t.value=o},F=()=>{_("update:value",t.value),_("submit",t.value)},j=()=>{_("cancel")};return x(()=>r.value,()=>{d()},{deep:!0}),x(()=>r.schema,()=>{d()},{deep:!0}),J(()=>{d()}),(o,n)=>{const s=m("a-input"),i=m("a-form-item"),B=m("a-input-number"),M=m("a-select-option"),N=m("a-select"),C=m("a-switch"),w=m("a-date-picker"),O=m("a-textarea"),$=m("a-button"),S=m("a-form");return p(),v("div",z,[c(S,{model:t.value,"label-col":{span:6},"wrapper-col":{span:18},onFinish:F},{default:u(()=>[(p(!0),v(g,null,Y(D.value,(e,a)=>(p(),v(g,{key:a},[e.type==="string"&&!e.enum&&!e.format?(p(),h(i,{key:0,label:e.title||a,name:a,rules:[{required:e.required,message:`请输入${e.title||a}`}],help:e.description},{default:u(()=>[c(s,{value:t.value[a],"onUpdate:value":l=>t.value[a]=l,placeholder:e.placeholder||`请输入${e.title||a}`},null,8,["value","onUpdate:value","placeholder"])]),_:2},1032,["label","name","rules","help"])):e.type==="number"||e.type==="integer"?(p(),h(i,{key:1,label:e.title||a,name:a,rules:[{required:e.required,message:`请输入${e.title||a}`}],help:e.description},{default:u(()=>[c(B,{value:t.value[a],"onUpdate:value":l=>t.value[a]=l,min:e.minimum,max:e.maximum,step:e.type==="integer"?1:.1,style:{width:"100%"}},null,8,["value","onUpdate:value","min","max","step"])]),_:2},1032,["label","name","rules","help"])):e.enum?(p(),h(i,{key:2,label:e.title||a,name:a,rules:[{required:e.required,message:`请选择${e.title||a}`}],help:e.description},{default:u(()=>[c(N,{value:t.value[a],"onUpdate:value":l=>t.value[a]=l,placeholder:`请选择${e.title||a}`,style:{width:"100%"}},{default:u(()=>[(p(!0),v(g,null,Y(e.enum,(l,b)=>(p(),h(M,{key:b,value:l},{default:u(()=>[q(V(e.enumNames&&e.enumNames[b]?e.enumNames[b]:l),1)]),_:2},1032,["value"]))),128))]),_:2},1032,["value","onUpdate:value","placeholder"])]),_:2},1032,["label","name","rules","help"])):e.type==="boolean"?(p(),h(i,{key:3,label:e.title||a,name:a,help:e.description},{default:u(()=>[c(C,{checked:t.value[a],"onUpdate:checked":l=>t.value[a]=l},null,8,["checked","onUpdate:checked"])]),_:2},1032,["label","name","help"])):e.format==="date"?(p(),h(i,{key:4,label:e.title||a,name:a,rules:[{required:e.required,message:`请选择${e.title||a}`}],help:e.description},{default:u(()=>[c(w,{value:t.value[a],"onUpdate:value":l=>t.value[a]=l,style:{width:"100%"},"value-format":"YYYY-MM-DD"},null,8,["value","onUpdate:value"])]),_:2},1032,["label","name","rules","help"])):e.format==="date-time"?(p(),h(i,{key:5,label:e.title||a,name:a,rules:[{required:e.required,message:`请选择${e.title||a}`}],help:e.description},{default:u(()=>[c(w,{value:t.value[a],"onUpdate:value":l=>t.value[a]=l,"show-time":"",style:{width:"100%"},"value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["value","onUpdate:value"])]),_:2},1032,["label","name","rules","help"])):e.format==="textarea"?(p(),h(i,{key:6,label:e.title||a,name:a,rules:[{required:e.required,message:`请输入${e.title||a}`}],help:e.description},{default:u(()=>[c(O,{value:t.value[a],"onUpdate:value":l=>t.value[a]=l,placeholder:e.placeholder||`请输入${e.title||a}`,rows:4},null,8,["value","onUpdate:value","placeholder"])]),_:2},1032,["label","name","rules","help"])):(p(),h(i,{key:7,label:e.title||a,name:a,rules:[{required:e.required,message:`请输入${e.title||a}`}],help:e.description},{default:u(()=>[c(s,{value:t.value[a],"onUpdate:value":l=>t.value[a]=l,placeholder:e.placeholder||`请输入${e.title||a}`},null,8,["value","onUpdate:value","placeholder"])]),_:2},1032,["label","name","rules","help"]))],64))),128)),c(i,{"wrapper-col":{span:18,offset:6}},{default:u(()=>[c($,{type:"primary","html-type":"submit",loading:U.loading},{default:u(()=>[q("保存")]),_:1},8,["loading"]),c($,{style:{"margin-left":"10px"},onClick:j},{default:u(()=>[q("取消")]),_:1})]),_:1})]),_:1},8,["model"])])}}};export{T as _};
