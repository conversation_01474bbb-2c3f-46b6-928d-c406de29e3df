import{_ as Q,u as W,r as _,a as X,o as Z,b as i,c as r,d as h,w as t,e as o,h as ee,g as s,f as w,M as te,t as y,k as v,F as D,E as ae,l as S}from"./index-94aa741e.js";import{c as oe,A as le,f as ne}from"./AppLayout-b20be4c8.js";import{C as se}from"./ClearOutlined-a8aadfea.js";const ue={class:"action-bar"},re=["onClick"],ce={key:1},ie={key:3},de={key:4},_e={key:0},pe={__name:"AuditListView",setup(ve){const g=W(),C=_(!1),A=_([]),z=_(0),f=_(1),k=_(10),n=X({username:"",action:void 0,resource_type:void 0,resource_name:""}),p=_([]),b=_(!1),x=_(null),U=[{title:"用户名",dataIndex:"username",key:"username",width:120},{title:"操作类型",key:"action",width:100},{title:"资源类型",key:"resource_type",width:100},{title:"资源ID",key:"resource_id",width:80},{title:"资源名称",key:"resource_name",width:150,ellipsis:!0,tooltip:!0},{title:"IP地址",dataIndex:"ip_address",key:"ip_address",width:120},{title:"操作时间",key:"created_at",width:180},{title:"详情",key:"details",width:100}];Z(()=>{m()});const m=async()=>{C.value=!0;try{const e={page:f.value,size:k.value};n.username&&(e.username=n.username),n.action&&(e.action=n.action),n.resource_type&&(e.resource_type=n.resource_type),n.resource_name&&(e.resource_name=n.resource_name),p.value&&p.value.length===2&&(e.start_time=p.value[0].valueOf(),e.end_time=p.value[1].valueOf()),await oe({method:"get",url:"/api/v1/audit",params:e,onSuccess:(a,c)=>{A.value=a,z.value=c.total},errorMessage:"获取审计日志失败"})}finally{C.value=!1}},V=()=>{f.value=1,m()},I=()=>{n.username="",n.action=void 0,n.resource_type=void 0,n.resource_name="",p.value=[],f.value=1,m()},N=()=>{V()},T=(e,a)=>{f.value=e,k.value=a,m()},B=(e,a)=>{f.value=1,k.value=a,m()},L=e=>({create:"创建",update:"更新",delete:"删除",deploy:"发布",rollback:"回滚"})[e]||e,P=e=>({create:"green",update:"blue",delete:"red",deploy:"purple",rollback:"orange"})[e]||"default",Y=e=>({user:"用户",tool:"工具",func:"函数",config:"配置"})[e]||e,E=e=>({user:"cyan",tool:"blue",func:"green",config:"purple"})[e]||"default",F=e=>{const{resource_type:a,resource_id:c}=e;if(c)switch(a){case"user":g.push(`/user/${c}/edit`);break;case"tool":g.push(`/tool/${c}`);break;case"func":g.push(`/func/${c}`);break;case"config":g.push(`/config/${c}`);break}},H=e=>{x.value=e,b.value=!0};return(e,a)=>{const c=i("a-input"),d=i("a-select-option"),O=i("a-select"),J=i("a-range-picker"),R=i("a-space"),M=i("a-button"),$=i("a-tag"),j=i("a-table"),q=i("a-modal"),G=i("a-card");return r(),h(le,{"current-page-key":"audit"},{default:t(()=>[o(G,{title:"审计日志"},{default:t(()=>[ee("div",ue,[o(R,null,{default:t(()=>[o(c,{value:n.username,"onUpdate:value":a[0]||(a[0]=l=>n.username=l),placeholder:"用户名",style:{width:"150px"},"allow-clear":""},null,8,["value"]),o(O,{value:n.action,"onUpdate:value":a[1]||(a[1]=l=>n.action=l),placeholder:"操作类型",style:{width:"120px"},"allow-clear":""},{default:t(()=>[o(d,{value:"create"},{default:t(()=>[s("创建")]),_:1}),o(d,{value:"update"},{default:t(()=>[s("更新")]),_:1}),o(d,{value:"delete"},{default:t(()=>[s("删除")]),_:1}),o(d,{value:"deploy"},{default:t(()=>[s("发布")]),_:1}),o(d,{value:"rollback"},{default:t(()=>[s("回滚")]),_:1})]),_:1},8,["value"]),o(O,{value:n.resource_type,"onUpdate:value":a[2]||(a[2]=l=>n.resource_type=l),placeholder:"资源类型",style:{width:"120px"},"allow-clear":""},{default:t(()=>[o(d,{value:"user"},{default:t(()=>[s("用户")]),_:1}),o(d,{value:"tool"},{default:t(()=>[s("工具")]),_:1}),o(d,{value:"func"},{default:t(()=>[s("函数")]),_:1}),o(d,{value:"config"},{default:t(()=>[s("配置")]),_:1})]),_:1},8,["value"]),o(c,{value:n.resource_name,"onUpdate:value":a[3]||(a[3]=l=>n.resource_name=l),placeholder:"资源名称",style:{width:"150px"},"allow-clear":""},null,8,["value"]),o(J,{value:p.value,"onUpdate:value":a[4]||(a[4]=l=>p.value=l),"show-time":"",format:"YYYY-MM-DD HH:mm:ss",onChange:N,placeholder:["开始时间","结束时间"]},null,8,["value"])]),_:1}),o(R,null,{default:t(()=>[o(M,{type:"primary",onClick:V},{icon:t(()=>[o(w(te))]),default:t(()=>[s(" 查询 ")]),_:1}),o(M,{onClick:I},{icon:t(()=>[o(w(se))]),default:t(()=>[s(" 重置 ")]),_:1})]),_:1})]),o(j,{columns:U,"data-source":A.value,loading:C.value,pagination:{current:f.value,pageSize:k.value,total:z.value,onChange:T,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:B,showTotal:l=>`共 ${l} 条记录`},"row-key":l=>l.id},{bodyCell:t(({column:l,record:u})=>[l.key==="action"?(r(),h($,{key:0,color:P(u.action)},{default:t(()=>[s(y(L(u.action)),1)]),_:2},1032,["color"])):l.key==="resource_type"?(r(),h($,{key:1,color:E(u.resource_type)},{default:t(()=>[s(y(Y(u.resource_type)),1)]),_:2},1032,["color"])):l.key==="resource_id"?(r(),v(D,{key:2},[u.resource_id?(r(),v("a",{key:0,onClick:K=>F(u)},y(u.resource_id),9,re)):(r(),v("span",ce,"-"))],64)):l.key==="resource_name"?(r(),v("span",ie,y(u.resource_name||"-"),1)):l.key==="created_at"?(r(),v("span",de,y(w(ne)(u.created_at)),1)):l.key==="details"?(r(),v(D,{key:5},[u.details?(r(),h(M,{key:0,type:"link",size:"small",onClick:K=>H(u)},{icon:t(()=>[o(w(ae))]),default:t(()=>[s(" 查看 ")]),_:2},1032,["onClick"])):S("",!0)],64)):S("",!0)]),_:1},8,["data-source","loading","pagination","row-key"]),o(q,{open:b.value,"onUpdate:open":a[5]||(a[5]=l=>b.value=l),title:"操作详情",width:"800px",footer:null},{default:t(()=>[x.value?(r(),v("pre",_e,y(JSON.stringify(x.value.details,null,2)),1)):S("",!0)]),_:1},8,["open"])]),_:1})]),_:1})}}},ge=Q(pe,[["__scopeId","data-v-a6d28363"]]);export{ge as default};
