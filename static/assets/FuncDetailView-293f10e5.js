import{_ as ue,u as de,x as ve,y as _e,r as c,o as fe,b as v,c as i,d,w as e,h as s,t as n,g as p,l as z,e as a,f as u,s as pe,k as y,F as V,z as M,p as he,m as ye,q as j}from"./index-94aa741e.js";import{c as m,A as me,f as K,F as G,T as ke}from"./AppLayout-b20be4c8.js";/* empty css                                                     */import{R as J}from"./RollbackOutlined-27c22304.js";const _=$=>(he("data-v-e15b30e5"),$=$(),ye(),$),be={class:"info-container"},ge={class:"info-row"},we=_(()=>s("div",{class:"info-label"},"函数名称:",-1)),$e={class:"info-value"},Ce={class:"info-row"},xe=_(()=>s("div",{class:"info-label"},"函数描述:",-1)),Se={class:"info-value"},Ve={class:"info-row"},Me=_(()=>s("div",{class:"info-label"},"当前版本:",-1)),De={class:"info-value"},Ee={class:"info-row"},Re=_(()=>s("div",{class:"info-label"},"创建时间:",-1)),Te={class:"info-value"},Fe={class:"info-row"},Ie=_(()=>s("div",{class:"info-label"},"更新时间:",-1)),Ne={class:"info-value"},Be={class:"info-row"},Oe=_(()=>s("div",{class:"info-label"},"创建人:",-1)),ze={class:"info-value"},Ke={class:"info-row"},Ue=_(()=>s("div",{class:"info-label"},"更新人:",-1)),Ae={class:"info-value"},He={class:"code-container"},Le={class:"code-display"},Pe={key:1,class:"version-list"},qe={class:"version-row"},je=_(()=>s("div",{class:"version-label"},"版本:",-1)),Ge=_(()=>s("div",{class:"version-label time-label"},"发布时间:",-1)),Je={class:"version-time"},Qe={key:0,class:"version-description"},We={class:"code-display"},Xe={class:"rollback-warning"},Ye={class:"rollback-code-container"},Ze={class:"rollback-code-header"},es={class:"code-display"},ss=["onClick"],os=["onClick"],ts=["onClick"],as={__name:"FuncDetailView",setup($){const g=de(),Q=ve(),f=_e(()=>Q.params.id),U=c(!1),r=c(null),D=c([]),E=c([]),R=c([]),T=c([]),A=c(!1),W=c(1),X=c(10),Y=c(0),H=c("basic"),C=c(!1),w=c(!1),h=c(null),k=c(""),Z=c(null),F=c(!1);fe(()=>{L(),ee(),se(),oe()});const L=async()=>{U.value=!0;try{await m({method:"get",url:`/api/v1/func/${f.value}`,onSuccess:t=>{r.value=t},errorMessage:"获取函数数据失败"})}finally{U.value=!1}},ee=async()=>{try{await m({method:"get",url:`/api/v1/func/${f.value}/usage`,onSuccess:t=>{t&&(R.value=t.tools||[],E.value=t.funcs||[])},errorMessage:"获取使用信息失败"})}catch(t){console.error("Error fetching usage info:",t)}},se=async()=>{try{await m({method:"get",url:`/api/v1/func/${f.value}/depend`,onSuccess:t=>{D.value=t||[]},errorMessage:"获取依赖函数失败"})}catch(t){console.error("Error fetching dependencies:",t)}},oe=async()=>{A.value=!0;try{await m({method:"get",url:`/api/v1/func/${f.value}/deploy/history`,params:{page:W.value,size:X.value},onSuccess:(t,l)=>{T.value=t,Y.value=l.total},errorMessage:"获取发布历史失败"})}finally{A.value=!1}},te=async t=>{try{if(h.value=t.version,Z.value=t,t.code){k.value=t.code,w.value=!0;return}await m({method:"get",url:`/api/v1/func/${f.value}/version/${t.version}`,onSuccess:l=>{k.value=l.code||"// No code available for this version",w.value=!0},errorMessage:"获取函数代码失败"})}catch(l){console.error("Error fetching version code:",l),j.error("获取函数代码失败")}},ae=async()=>{if(h.value){F.value=!0;try{await m({method:"post",url:`/api/v1/func/${f.value}/deploy/rollback/${h.value}`,successMessage:"回滚成功",errorMessage:"回滚失败",onSuccess:()=>{L(),w.value=!1}})}catch(t){console.error("Error rolling back function:",t)}finally{F.value=!1}}},ne=async t=>{try{if(h.value=t.version,t.code){k.value=t.code,C.value=!0;return}await m({method:"get",url:`/api/v1/func/${f.value}/version/${t.version}`,onSuccess:l=>{k.value=l.code||"// No code available for this version",C.value=!0},errorMessage:"获取函数代码失败"})}catch(l){console.error("Error fetching version code:",l),j.error("获取函数代码失败")}};return(t,l)=>{const P=v("a-tag"),I=v("a-button"),le=v("a-space"),b=v("a-tab-pane"),x=v("a-empty"),q=v("a-modal"),ie=v("a-alert"),N=v("a-list-item-meta"),B=v("a-list-item"),O=v("a-list"),ce=v("a-tabs"),re=v("a-card");return i(),d(me,{"current-page-key":"func"},{default:e(()=>[r.value?(i(),d(re,{key:0},{title:e(()=>[s("span",null,"函数详情 - "+n(r.value.name),1),r.value.current_version?(i(),d(P,{key:0,color:"blue",class:"version-tag"},{default:e(()=>[p("v"+n(r.value.current_version),1)]),_:1})):z("",!0)]),extra:e(()=>[a(le,null,{default:e(()=>[a(I,{onClick:l[0]||(l[0]=o=>u(g).push("/func"))},{icon:e(()=>[a(u(J))]),default:e(()=>[p(" 返回 ")]),_:1}),a(I,{type:"primary",onClick:l[1]||(l[1]=o=>u(g).push(`/func/${f.value}/edit`))},{icon:e(()=>[a(u(pe))]),default:e(()=>[p(" 编辑 ")]),_:1})]),_:1})]),default:e(()=>[a(ce,{activeKey:H.value,"onUpdate:activeKey":l[4]||(l[4]=o=>H.value=o)},{default:e(()=>[a(b,{key:"basic",tab:"基本信息"},{default:e(()=>[s("div",be,[s("div",ge,[we,s("div",$e,n(r.value.name),1)]),s("div",Ce,[xe,s("div",Se,n(r.value.description||"无描述"),1)]),s("div",Ve,[Me,s("div",De,n(r.value.current_version||"未发布"),1)]),s("div",Ee,[Re,s("div",Te,n(u(K)(r.value.created_at)),1)]),s("div",Fe,[Ie,s("div",Ne,n(u(K)(r.value.updated_at)),1)]),s("div",Be,[Oe,s("div",ze,n(r.value.created_by||"-"),1)]),s("div",Ke,[Ue,s("div",Ae,n(r.value.updated_by||"-"),1)])])]),_:1}),a(b,{key:"code",tab:"函数代码"},{default:e(()=>[s("div",He,[s("pre",Le,n(r.value.code),1)])]),_:1}),a(b,{key:"history",tab:"发布历史"},{default:e(()=>[T.value.length?(i(),y("div",Pe,[(i(!0),y(V,null,M(T.value,o=>(i(),y("div",{key:o.id,class:"version-item"},[s("div",qe,[je,a(P,{class:"version-tag",color:r.value.current_version===o.version?"blue":"default",onClick:S=>ne(o)},{default:e(()=>[p(" v"+n(o.version),1)]),_:2},1032,["color","onClick"]),Ge,s("span",Je,n(u(K)(o.created_at)),1),a(I,{type:"primary",size:"small",disabled:r.value.current_version===o.version,class:"rollback-button",onClick:S=>te(o)},{icon:e(()=>[a(u(J))]),default:e(()=>[p(" 回滚 ")]),_:2},1032,["disabled","onClick"])]),o.description?(i(),y("div",Qe,n(o.description),1)):z("",!0)]))),128))])):(i(),d(x,{key:0,description:"无发布历史"})),a(q,{open:C.value,"onUpdate:open":l[2]||(l[2]=o=>C.value=o),title:`函数代码 (v${h.value})`,width:"800px",footer:null},{default:e(()=>[s("pre",We,n(k.value),1)]),_:1},8,["open","title"]),a(q,{open:w.value,"onUpdate:open":l[3]||(l[3]=o=>w.value=o),title:`确认回滚到版本 v${h.value}?`,width:"800px","confirm-loading":F.value,"ok-text":"确认回滚","cancel-text":"取消",onOk:ae},{default:e(()=>[s("div",Xe,[a(ie,{message:"警告",description:"回滚操作将使当前函数代码替换为选中版本的代码，请确认该操作。",type:"warning","show-icon":"",style:{"margin-bottom":"16px"}})]),s("div",Ye,[s("div",Ze,"版本 v"+n(h.value)+" 的代码:",1),s("pre",es,n(k.value),1)])]),_:1},8,["open","title","confirm-loading"])]),_:1}),a(b,{key:"dependencies",tab:"依赖函数"},{default:e(()=>[D.value.length?(i(),d(O,{key:1},{default:e(()=>[(i(!0),y(V,null,M(D.value,o=>(i(),d(B,{key:o.id},{default:e(()=>[a(N,null,{avatar:e(()=>[a(u(G),{class:"func-icon"})]),title:e(()=>[s("a",{class:"func-link",onClick:S=>u(g).push(`/func/${o.id}`)},n(o.name),9,ss)]),description:e(()=>[p(n(o.description||"无描述"),1)]),_:2},1024)]),_:2},1024))),128))]),_:1})):(i(),d(x,{key:0,description:"无依赖函数"}))]),_:1}),a(b,{key:"tools",tab:"关联工具"},{default:e(()=>[R.value.length?(i(),d(O,{key:1},{default:e(()=>[(i(!0),y(V,null,M(R.value,o=>(i(),d(B,{key:o.id},{default:e(()=>[a(N,null,{avatar:e(()=>[a(u(ke),{class:"tool-icon"})]),title:e(()=>[s("a",{class:"tool-link",onClick:S=>u(g).push(`/tool/${o.id}`)},n(o.name),9,os)]),description:e(()=>[p(n(o.description||"无描述"),1)]),_:2},1024)]),_:2},1024))),128))]),_:1})):(i(),d(x,{key:0,description:"无关联工具"}))]),_:1}),a(b,{key:"dependent-funcs",tab:"关联函数"},{default:e(()=>[E.value.length?(i(),d(O,{key:1},{default:e(()=>[(i(!0),y(V,null,M(E.value,o=>(i(),d(B,{key:o.id},{default:e(()=>[a(N,null,{avatar:e(()=>[a(u(G),{class:"func-icon"})]),title:e(()=>[s("a",{class:"func-link",onClick:S=>u(g).push(`/func/${o.id}`)},n(o.name),9,ts)]),description:e(()=>[p(n(o.description||"无描述"),1)]),_:2},1024)]),_:2},1024))),128))]),_:1})):(i(),d(x,{key:0,description:"无关联函数"}))]),_:1})]),_:1},8,["activeKey"])]),_:1})):z("",!0)]),_:1})}}},rs=ue(as,[["__scopeId","data-v-e15b30e5"]]);export{rs as default};
