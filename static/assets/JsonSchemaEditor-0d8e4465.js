import{_ as pn}from"./MonacoEditor-7a57fdbb.js";import{e as p,A as mn,b as j,T as gn,G as vn,H as Pt,n as qt,_ as bn,r as ve,y as yn,o as _n,B as At,q as at,c as K,k as be,h as fe,w as v,g as P,f as Pe,P as wn,d as Ae,t as it,l as ye,s as En,v as Dn,F as lt,p as Sn,m as Cn}from"./index-94aa741e.js";var xn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M904 160H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0 624H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0-312H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z"}}]},name:"menu",theme:"outlined"};const On=xn;function kt(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?Object(arguments[e]):{},o=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),o.forEach(function(r){In(t,r,n[r])})}return t}function In(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var St=function(e,n){var o=kt({},e,n.attrs);return p(mn,kt({},o,{icon:On}),null)};St.displayName="MenuOutlined";St.inheritAttrs=!1;const Tn=St;/**!
 * Sortable 1.14.0
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function Nt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),n.push.apply(n,o)}return n}function re(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Nt(Object(n),!0).forEach(function(o){Pn(t,o,n[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Nt(Object(n)).forEach(function(o){Object.defineProperty(t,o,Object.getOwnPropertyDescriptor(n,o))})}return t}function ze(t){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?ze=function(e){return typeof e}:ze=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ze(t)}function Pn(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function se(){return se=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},se.apply(this,arguments)}function An(t,e){if(t==null)return{};var n={},o=Object.keys(t),r,a;for(a=0;a<o.length;a++)r=o[a],!(e.indexOf(r)>=0)&&(n[r]=t[r]);return n}function kn(t,e){if(t==null)return{};var n=An(t,e),o,r;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(r=0;r<a.length;r++)o=a[r],!(e.indexOf(o)>=0)&&Object.prototype.propertyIsEnumerable.call(t,o)&&(n[o]=t[o])}return n}var Nn="1.14.0";function le(t){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(t)}var ue=le(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),Ue=le(/Edge/i),Mt=le(/firefox/i),Fe=le(/safari/i)&&!le(/chrome/i)&&!le(/android/i),$t=le(/iP(ad|od|hone)/i),Mn=le(/chrome/i)&&le(/android/i),zt={capture:!1,passive:!1};function x(t,e,n){t.addEventListener(e,n,!ue&&zt)}function D(t,e,n){t.removeEventListener(e,n,!ue&&zt)}function Ze(t,e){if(e){if(e[0]===">"&&(e=e.substring(1)),t)try{if(t.matches)return t.matches(e);if(t.msMatchesSelector)return t.msMatchesSelector(e);if(t.webkitMatchesSelector)return t.webkitMatchesSelector(e)}catch{return!1}return!1}}function Fn(t){return t.host&&t!==document&&t.host.nodeType?t.host:t.parentNode}function ne(t,e,n,o){if(t){n=n||document;do{if(e!=null&&(e[0]===">"?t.parentNode===n&&Ze(t,e):Ze(t,e))||o&&t===n)return t;if(t===n)break}while(t=Fn(t))}return null}var Ft=/\s+/g;function $(t,e,n){if(t&&e)if(t.classList)t.classList[n?"add":"remove"](e);else{var o=(" "+t.className+" ").replace(Ft," ").replace(" "+e+" "," ");t.className=(o+(n?" "+e:"")).replace(Ft," ")}}function m(t,e,n){var o=t&&t.style;if(o){if(n===void 0)return document.defaultView&&document.defaultView.getComputedStyle?n=document.defaultView.getComputedStyle(t,""):t.currentStyle&&(n=t.currentStyle),e===void 0?n:n[e];!(e in o)&&e.indexOf("webkit")===-1&&(e="-webkit-"+e),o[e]=n+(typeof n=="string"?"":"px")}}function Ie(t,e){var n="";if(typeof t=="string")n=t;else do{var o=m(t,"transform");o&&o!=="none"&&(n=o+" "+n)}while(!e&&(t=t.parentNode));var r=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return r&&new r(n)}function Wt(t,e,n){if(t){var o=t.getElementsByTagName(e),r=0,a=o.length;if(n)for(;r<a;r++)n(o[r],r);return o}return[]}function oe(){var t=document.scrollingElement;return t||document.documentElement}function M(t,e,n,o,r){if(!(!t.getBoundingClientRect&&t!==window)){var a,l,s,i,u,c,f;if(t!==window&&t.parentNode&&t!==oe()?(a=t.getBoundingClientRect(),l=a.top,s=a.left,i=a.bottom,u=a.right,c=a.height,f=a.width):(l=0,s=0,i=window.innerHeight,u=window.innerWidth,c=window.innerHeight,f=window.innerWidth),(e||n)&&t!==window&&(r=r||t.parentNode,!ue))do if(r&&r.getBoundingClientRect&&(m(r,"transform")!=="none"||n&&m(r,"position")!=="static")){var w=r.getBoundingClientRect();l-=w.top+parseInt(m(r,"border-top-width")),s-=w.left+parseInt(m(r,"border-left-width")),i=l+a.height,u=s+a.width;break}while(r=r.parentNode);if(o&&t!==window){var S=Ie(r||t),E=S&&S.a,C=S&&S.d;S&&(l/=C,s/=E,f/=E,c/=C,i=l+c,u=s+f)}return{top:l,left:s,bottom:i,right:u,width:f,height:c}}}function Lt(t,e,n){for(var o=me(t,!0),r=M(t)[e];o;){var a=M(o)[n],l=void 0;if(n==="top"||n==="left"?l=r>=a:l=r<=a,!l)return o;if(o===oe())break;o=me(o,!1)}return!1}function Te(t,e,n,o){for(var r=0,a=0,l=t.children;a<l.length;){if(l[a].style.display!=="none"&&l[a]!==g.ghost&&(o||l[a]!==g.dragged)&&ne(l[a],n.draggable,t,!1)){if(r===e)return l[a];r++}a++}return null}function Ct(t,e){for(var n=t.lastElementChild;n&&(n===g.ghost||m(n,"display")==="none"||e&&!Ze(n,e));)n=n.previousElementSibling;return n||null}function Z(t,e){var n=0;if(!t||!t.parentNode)return-1;for(;t=t.previousElementSibling;)t.nodeName.toUpperCase()!=="TEMPLATE"&&t!==g.clone&&(!e||Ze(t,e))&&n++;return n}function Rt(t){var e=0,n=0,o=oe();if(t)do{var r=Ie(t),a=r.a,l=r.d;e+=t.scrollLeft*a,n+=t.scrollTop*l}while(t!==o&&(t=t.parentNode));return[e,n]}function Ln(t,e){for(var n in t)if(t.hasOwnProperty(n)){for(var o in e)if(e.hasOwnProperty(o)&&e[o]===t[n][o])return Number(n)}return-1}function me(t,e){if(!t||!t.getBoundingClientRect)return oe();var n=t,o=!1;do if(n.clientWidth<n.scrollWidth||n.clientHeight<n.scrollHeight){var r=m(n);if(n.clientWidth<n.scrollWidth&&(r.overflowX=="auto"||r.overflowX=="scroll")||n.clientHeight<n.scrollHeight&&(r.overflowY=="auto"||r.overflowY=="scroll")){if(!n.getBoundingClientRect||n===document.body)return oe();if(o||e)return n;o=!0}}while(n=n.parentNode);return oe()}function Rn(t,e){if(t&&e)for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}function st(t,e){return Math.round(t.top)===Math.round(e.top)&&Math.round(t.left)===Math.round(e.left)&&Math.round(t.height)===Math.round(e.height)&&Math.round(t.width)===Math.round(e.width)}var Le;function Gt(t,e){return function(){if(!Le){var n=arguments,o=this;n.length===1?t.call(o,n[0]):t.apply(o,n),Le=setTimeout(function(){Le=void 0},e)}}}function jn(){clearTimeout(Le),Le=void 0}function Jt(t,e,n){t.scrollLeft+=e,t.scrollTop+=n}function Kt(t){var e=window.Polymer,n=window.jQuery||window.Zepto;return e&&e.dom?e.dom(t).cloneNode(!0):n?n(t).clone(!0)[0]:t.cloneNode(!0)}var W="Sortable"+new Date().getTime();function Bn(){var t=[],e;return{captureAnimationState:function(){if(t=[],!!this.options.animation){var o=[].slice.call(this.el.children);o.forEach(function(r){if(!(m(r,"display")==="none"||r===g.ghost)){t.push({target:r,rect:M(r)});var a=re({},t[t.length-1].rect);if(r.thisAnimationDuration){var l=Ie(r,!0);l&&(a.top-=l.f,a.left-=l.e)}r.fromRect=a}})}},addAnimationState:function(o){t.push(o)},removeAnimationState:function(o){t.splice(Ln(t,{target:o}),1)},animateAll:function(o){var r=this;if(!this.options.animation){clearTimeout(e),typeof o=="function"&&o();return}var a=!1,l=0;t.forEach(function(s){var i=0,u=s.target,c=u.fromRect,f=M(u),w=u.prevFromRect,S=u.prevToRect,E=s.rect,C=Ie(u,!0);C&&(f.top-=C.f,f.left-=C.e),u.toRect=f,u.thisAnimationDuration&&st(w,f)&&!st(c,f)&&(E.top-f.top)/(E.left-f.left)===(c.top-f.top)/(c.left-f.left)&&(i=Xn(E,w,S,r.options)),st(f,c)||(u.prevFromRect=c,u.prevToRect=f,i||(i=r.options.animation),r.animate(u,E,f,i)),i&&(a=!0,l=Math.max(l,i),clearTimeout(u.animationResetTimer),u.animationResetTimer=setTimeout(function(){u.animationTime=0,u.prevFromRect=null,u.fromRect=null,u.prevToRect=null,u.thisAnimationDuration=null},i),u.thisAnimationDuration=i)}),clearTimeout(e),a?e=setTimeout(function(){typeof o=="function"&&o()},l):typeof o=="function"&&o(),t=[]},animate:function(o,r,a,l){if(l){m(o,"transition",""),m(o,"transform","");var s=Ie(this.el),i=s&&s.a,u=s&&s.d,c=(r.left-a.left)/(i||1),f=(r.top-a.top)/(u||1);o.animatingX=!!c,o.animatingY=!!f,m(o,"transform","translate3d("+c+"px,"+f+"px,0)"),this.forRepaintDummy=Un(o),m(o,"transition","transform "+l+"ms"+(this.options.easing?" "+this.options.easing:"")),m(o,"transform","translate3d(0,0,0)"),typeof o.animated=="number"&&clearTimeout(o.animated),o.animated=setTimeout(function(){m(o,"transition",""),m(o,"transform",""),o.animated=!1,o.animatingX=!1,o.animatingY=!1},l)}}}}function Un(t){return t.offsetWidth}function Xn(t,e,n,o){return Math.sqrt(Math.pow(e.top-t.top,2)+Math.pow(e.left-t.left,2))/Math.sqrt(Math.pow(e.top-n.top,2)+Math.pow(e.left-n.left,2))*o.animation}var Se=[],ut={initializeByDefault:!0},Xe={mount:function(e){for(var n in ut)ut.hasOwnProperty(n)&&!(n in e)&&(e[n]=ut[n]);Se.forEach(function(o){if(o.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")}),Se.push(e)},pluginEvent:function(e,n,o){var r=this;this.eventCanceled=!1,o.cancel=function(){r.eventCanceled=!0};var a=e+"Global";Se.forEach(function(l){n[l.pluginName]&&(n[l.pluginName][a]&&n[l.pluginName][a](re({sortable:n},o)),n.options[l.pluginName]&&n[l.pluginName][e]&&n[l.pluginName][e](re({sortable:n},o)))})},initializePlugins:function(e,n,o,r){Se.forEach(function(s){var i=s.pluginName;if(!(!e.options[i]&&!s.initializeByDefault)){var u=new s(e,n,e.options);u.sortable=e,u.options=e.options,e[i]=u,se(o,u.defaults)}});for(var a in e.options)if(e.options.hasOwnProperty(a)){var l=this.modifyOption(e,a,e.options[a]);typeof l<"u"&&(e.options[a]=l)}},getEventProperties:function(e,n){var o={};return Se.forEach(function(r){typeof r.eventProperties=="function"&&se(o,r.eventProperties.call(n[r.pluginName],e))}),o},modifyOption:function(e,n,o){var r;return Se.forEach(function(a){e[a.pluginName]&&a.optionListeners&&typeof a.optionListeners[n]=="function"&&(r=a.optionListeners[n].call(e[a.pluginName],o))}),r}};function Yn(t){var e=t.sortable,n=t.rootEl,o=t.name,r=t.targetEl,a=t.cloneEl,l=t.toEl,s=t.fromEl,i=t.oldIndex,u=t.newIndex,c=t.oldDraggableIndex,f=t.newDraggableIndex,w=t.originalEvent,S=t.putSortable,E=t.extraEventProperties;if(e=e||n&&n[W],!!e){var C,H=e.options,Q="on"+o.charAt(0).toUpperCase()+o.substr(1);window.CustomEvent&&!ue&&!Ue?C=new CustomEvent(o,{bubbles:!0,cancelable:!0}):(C=document.createEvent("Event"),C.initEvent(o,!0,!0)),C.to=l||n,C.from=s||n,C.item=r||n,C.clone=a,C.oldIndex=i,C.newIndex=u,C.oldDraggableIndex=c,C.newDraggableIndex=f,C.originalEvent=w,C.pullMode=S?S.lastPutMode:void 0;var F=re(re({},E),Xe.getEventProperties(o,e));for(var Y in F)C[Y]=F[Y];n&&n.dispatchEvent(C),H[Q]&&H[Q].call(e,C)}}var Vn=["evt"],V=function(e,n){var o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},r=o.evt,a=kn(o,Vn);Xe.pluginEvent.bind(g)(e,n,re({dragEl:d,parentEl:A,ghostEl:_,rootEl:O,nextEl:Ee,lastDownEl:We,cloneEl:k,cloneHidden:pe,dragStarted:ke,putSortable:L,activeSortable:g.active,originalEvent:r,oldIndex:Oe,oldDraggableIndex:Re,newIndex:z,newDraggableIndex:he,hideGhostForTarget:tn,unhideGhostForTarget:nn,cloneNowHidden:function(){pe=!0},cloneNowShown:function(){pe=!1},dispatchSortableEvent:function(s){X({sortable:n,name:s,originalEvent:r})}},a))};function X(t){Yn(re({putSortable:L,cloneEl:k,targetEl:d,rootEl:O,oldIndex:Oe,oldDraggableIndex:Re,newIndex:z,newDraggableIndex:he},t))}var d,A,_,O,Ee,We,k,pe,Oe,z,Re,he,Ve,L,xe=!1,Qe=!1,et=[],_e,ee,dt,ct,jt,Bt,ke,Ce,je,Be=!1,He=!1,Ge,R,ft=[],yt=!1,tt=[],ot=typeof document<"u",qe=$t,Ut=Ue||ue?"cssFloat":"float",Hn=ot&&!Mn&&!$t&&"draggable"in document.createElement("div"),Zt=function(){if(ot){if(ue)return!1;var t=document.createElement("x");return t.style.cssText="pointer-events:auto",t.style.pointerEvents==="auto"}}(),Qt=function(e,n){var o=m(e),r=parseInt(o.width)-parseInt(o.paddingLeft)-parseInt(o.paddingRight)-parseInt(o.borderLeftWidth)-parseInt(o.borderRightWidth),a=Te(e,0,n),l=Te(e,1,n),s=a&&m(a),i=l&&m(l),u=s&&parseInt(s.marginLeft)+parseInt(s.marginRight)+M(a).width,c=i&&parseInt(i.marginLeft)+parseInt(i.marginRight)+M(l).width;if(o.display==="flex")return o.flexDirection==="column"||o.flexDirection==="column-reverse"?"vertical":"horizontal";if(o.display==="grid")return o.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(a&&s.float&&s.float!=="none"){var f=s.float==="left"?"left":"right";return l&&(i.clear==="both"||i.clear===f)?"vertical":"horizontal"}return a&&(s.display==="block"||s.display==="flex"||s.display==="table"||s.display==="grid"||u>=r&&o[Ut]==="none"||l&&o[Ut]==="none"&&u+c>r)?"vertical":"horizontal"},qn=function(e,n,o){var r=o?e.left:e.top,a=o?e.right:e.bottom,l=o?e.width:e.height,s=o?n.left:n.top,i=o?n.right:n.bottom,u=o?n.width:n.height;return r===s||a===i||r+l/2===s+u/2},$n=function(e,n){var o;return et.some(function(r){var a=r[W].options.emptyInsertThreshold;if(!(!a||Ct(r))){var l=M(r),s=e>=l.left-a&&e<=l.right+a,i=n>=l.top-a&&n<=l.bottom+a;if(s&&i)return o=r}}),o},en=function(e){function n(a,l){return function(s,i,u,c){var f=s.options.group.name&&i.options.group.name&&s.options.group.name===i.options.group.name;if(a==null&&(l||f))return!0;if(a==null||a===!1)return!1;if(l&&a==="clone")return a;if(typeof a=="function")return n(a(s,i,u,c),l)(s,i,u,c);var w=(l?s:i).options.group.name;return a===!0||typeof a=="string"&&a===w||a.join&&a.indexOf(w)>-1}}var o={},r=e.group;(!r||ze(r)!="object")&&(r={name:r}),o.name=r.name,o.checkPull=n(r.pull,!0),o.checkPut=n(r.put),o.revertClone=r.revertClone,e.group=o},tn=function(){!Zt&&_&&m(_,"display","none")},nn=function(){!Zt&&_&&m(_,"display","")};ot&&document.addEventListener("click",function(t){if(Qe)return t.preventDefault(),t.stopPropagation&&t.stopPropagation(),t.stopImmediatePropagation&&t.stopImmediatePropagation(),Qe=!1,!1},!0);var we=function(e){if(d){e=e.touches?e.touches[0]:e;var n=$n(e.clientX,e.clientY);if(n){var o={};for(var r in e)e.hasOwnProperty(r)&&(o[r]=e[r]);o.target=o.rootEl=n,o.preventDefault=void 0,o.stopPropagation=void 0,n[W]._onDragOver(o)}}},zn=function(e){d&&d.parentNode[W]._isOutsideThisEl(e.target)};function g(t,e){if(!(t&&t.nodeType&&t.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(t));this.el=t,this.options=e=se({},e),t[W]=this;var n={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(t.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return Qt(t,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(l,s){l.setData("Text",s.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:g.supportPointer!==!1&&"PointerEvent"in window&&!Fe,emptyInsertThreshold:5};Xe.initializePlugins(this,t,n);for(var o in n)!(o in e)&&(e[o]=n[o]);en(e);for(var r in this)r.charAt(0)==="_"&&typeof this[r]=="function"&&(this[r]=this[r].bind(this));this.nativeDraggable=e.forceFallback?!1:Hn,this.nativeDraggable&&(this.options.touchStartThreshold=1),e.supportPointer?x(t,"pointerdown",this._onTapStart):(x(t,"mousedown",this._onTapStart),x(t,"touchstart",this._onTapStart)),this.nativeDraggable&&(x(t,"dragover",this),x(t,"dragenter",this)),et.push(this.el),e.store&&e.store.get&&this.sort(e.store.get(this)||[]),se(this,Bn())}g.prototype={constructor:g,_isOutsideThisEl:function(e){!this.el.contains(e)&&e!==this.el&&(Ce=null)},_getDirection:function(e,n){return typeof this.options.direction=="function"?this.options.direction.call(this,e,n,d):this.options.direction},_onTapStart:function(e){if(e.cancelable){var n=this,o=this.el,r=this.options,a=r.preventOnFilter,l=e.type,s=e.touches&&e.touches[0]||e.pointerType&&e.pointerType==="touch"&&e,i=(s||e).target,u=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||i,c=r.filter;if(to(o),!d&&!(/mousedown|pointerdown/.test(l)&&e.button!==0||r.disabled)&&!u.isContentEditable&&!(!this.nativeDraggable&&Fe&&i&&i.tagName.toUpperCase()==="SELECT")&&(i=ne(i,r.draggable,o,!1),!(i&&i.animated)&&We!==i)){if(Oe=Z(i),Re=Z(i,r.draggable),typeof c=="function"){if(c.call(this,e,i,this)){X({sortable:n,rootEl:u,name:"filter",targetEl:i,toEl:o,fromEl:o}),V("filter",n,{evt:e}),a&&e.cancelable&&e.preventDefault();return}}else if(c&&(c=c.split(",").some(function(f){if(f=ne(u,f.trim(),o,!1),f)return X({sortable:n,rootEl:f,name:"filter",targetEl:i,fromEl:o,toEl:o}),V("filter",n,{evt:e}),!0}),c)){a&&e.cancelable&&e.preventDefault();return}r.handle&&!ne(u,r.handle,o,!1)||this._prepareDragStart(e,s,i)}}},_prepareDragStart:function(e,n,o){var r=this,a=r.el,l=r.options,s=a.ownerDocument,i;if(o&&!d&&o.parentNode===a){var u=M(o);if(O=a,d=o,A=d.parentNode,Ee=d.nextSibling,We=o,Ve=l.group,g.dragged=d,_e={target:d,clientX:(n||e).clientX,clientY:(n||e).clientY},jt=_e.clientX-u.left,Bt=_e.clientY-u.top,this._lastX=(n||e).clientX,this._lastY=(n||e).clientY,d.style["will-change"]="all",i=function(){if(V("delayEnded",r,{evt:e}),g.eventCanceled){r._onDrop();return}r._disableDelayedDragEvents(),!Mt&&r.nativeDraggable&&(d.draggable=!0),r._triggerDragStart(e,n),X({sortable:r,name:"choose",originalEvent:e}),$(d,l.chosenClass,!0)},l.ignore.split(",").forEach(function(c){Wt(d,c.trim(),ht)}),x(s,"dragover",we),x(s,"mousemove",we),x(s,"touchmove",we),x(s,"mouseup",r._onDrop),x(s,"touchend",r._onDrop),x(s,"touchcancel",r._onDrop),Mt&&this.nativeDraggable&&(this.options.touchStartThreshold=4,d.draggable=!0),V("delayStart",this,{evt:e}),l.delay&&(!l.delayOnTouchOnly||n)&&(!this.nativeDraggable||!(Ue||ue))){if(g.eventCanceled){this._onDrop();return}x(s,"mouseup",r._disableDelayedDrag),x(s,"touchend",r._disableDelayedDrag),x(s,"touchcancel",r._disableDelayedDrag),x(s,"mousemove",r._delayedDragTouchMoveHandler),x(s,"touchmove",r._delayedDragTouchMoveHandler),l.supportPointer&&x(s,"pointermove",r._delayedDragTouchMoveHandler),r._dragStartTimer=setTimeout(i,l.delay)}else i()}},_delayedDragTouchMoveHandler:function(e){var n=e.touches?e.touches[0]:e;Math.max(Math.abs(n.clientX-this._lastX),Math.abs(n.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){d&&ht(d),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;D(e,"mouseup",this._disableDelayedDrag),D(e,"touchend",this._disableDelayedDrag),D(e,"touchcancel",this._disableDelayedDrag),D(e,"mousemove",this._delayedDragTouchMoveHandler),D(e,"touchmove",this._delayedDragTouchMoveHandler),D(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,n){n=n||e.pointerType=="touch"&&e,!this.nativeDraggable||n?this.options.supportPointer?x(document,"pointermove",this._onTouchMove):n?x(document,"touchmove",this._onTouchMove):x(document,"mousemove",this._onTouchMove):(x(d,"dragend",this),x(O,"dragstart",this._onDragStart));try{document.selection?Je(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(e,n){if(xe=!1,O&&d){V("dragStarted",this,{evt:n}),this.nativeDraggable&&x(document,"dragover",zn);var o=this.options;!e&&$(d,o.dragClass,!1),$(d,o.ghostClass,!0),g.active=this,e&&this._appendGhost(),X({sortable:this,name:"start",originalEvent:n})}else this._nulling()},_emulateDragOver:function(){if(ee){this._lastX=ee.clientX,this._lastY=ee.clientY,tn();for(var e=document.elementFromPoint(ee.clientX,ee.clientY),n=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint(ee.clientX,ee.clientY),e!==n);)n=e;if(d.parentNode[W]._isOutsideThisEl(e),n)do{if(n[W]){var o=void 0;if(o=n[W]._onDragOver({clientX:ee.clientX,clientY:ee.clientY,target:e,rootEl:n}),o&&!this.options.dragoverBubble)break}e=n}while(n=n.parentNode);nn()}},_onTouchMove:function(e){if(_e){var n=this.options,o=n.fallbackTolerance,r=n.fallbackOffset,a=e.touches?e.touches[0]:e,l=_&&Ie(_,!0),s=_&&l&&l.a,i=_&&l&&l.d,u=qe&&R&&Rt(R),c=(a.clientX-_e.clientX+r.x)/(s||1)+(u?u[0]-ft[0]:0)/(s||1),f=(a.clientY-_e.clientY+r.y)/(i||1)+(u?u[1]-ft[1]:0)/(i||1);if(!g.active&&!xe){if(o&&Math.max(Math.abs(a.clientX-this._lastX),Math.abs(a.clientY-this._lastY))<o)return;this._onDragStart(e,!0)}if(_){l?(l.e+=c-(dt||0),l.f+=f-(ct||0)):l={a:1,b:0,c:0,d:1,e:c,f};var w="matrix(".concat(l.a,",").concat(l.b,",").concat(l.c,",").concat(l.d,",").concat(l.e,",").concat(l.f,")");m(_,"webkitTransform",w),m(_,"mozTransform",w),m(_,"msTransform",w),m(_,"transform",w),dt=c,ct=f,ee=a}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!_){var e=this.options.fallbackOnBody?document.body:O,n=M(d,!0,qe,!0,e),o=this.options;if(qe){for(R=e;m(R,"position")==="static"&&m(R,"transform")==="none"&&R!==document;)R=R.parentNode;R!==document.body&&R!==document.documentElement?(R===document&&(R=oe()),n.top+=R.scrollTop,n.left+=R.scrollLeft):R=oe(),ft=Rt(R)}_=d.cloneNode(!0),$(_,o.ghostClass,!1),$(_,o.fallbackClass,!0),$(_,o.dragClass,!0),m(_,"transition",""),m(_,"transform",""),m(_,"box-sizing","border-box"),m(_,"margin",0),m(_,"top",n.top),m(_,"left",n.left),m(_,"width",n.width),m(_,"height",n.height),m(_,"opacity","0.8"),m(_,"position",qe?"absolute":"fixed"),m(_,"zIndex","100000"),m(_,"pointerEvents","none"),g.ghost=_,e.appendChild(_),m(_,"transform-origin",jt/parseInt(_.style.width)*100+"% "+Bt/parseInt(_.style.height)*100+"%")}},_onDragStart:function(e,n){var o=this,r=e.dataTransfer,a=o.options;if(V("dragStart",this,{evt:e}),g.eventCanceled){this._onDrop();return}V("setupClone",this),g.eventCanceled||(k=Kt(d),k.draggable=!1,k.style["will-change"]="",this._hideClone(),$(k,this.options.chosenClass,!1),g.clone=k),o.cloneId=Je(function(){V("clone",o),!g.eventCanceled&&(o.options.removeCloneOnHide||O.insertBefore(k,d),o._hideClone(),X({sortable:o,name:"clone"}))}),!n&&$(d,a.dragClass,!0),n?(Qe=!0,o._loopId=setInterval(o._emulateDragOver,50)):(D(document,"mouseup",o._onDrop),D(document,"touchend",o._onDrop),D(document,"touchcancel",o._onDrop),r&&(r.effectAllowed="move",a.setData&&a.setData.call(o,r,d)),x(document,"drop",o),m(d,"transform","translateZ(0)")),xe=!0,o._dragStartId=Je(o._dragStarted.bind(o,n,e)),x(document,"selectstart",o),ke=!0,Fe&&m(document.body,"user-select","none")},_onDragOver:function(e){var n=this.el,o=e.target,r,a,l,s=this.options,i=s.group,u=g.active,c=Ve===i,f=s.sort,w=L||u,S,E=this,C=!1;if(yt)return;function H(ce,ge){V(ce,E,re({evt:e,isOwner:c,axis:S?"vertical":"horizontal",revert:l,dragRect:r,targetRect:a,canSort:f,fromSortable:w,target:o,completed:F,onMove:function(Ye,rt){return $e(O,n,d,r,Ye,M(Ye),e,rt)},changed:Y},ge))}function Q(){H("dragOverAnimationCapture"),E.captureAnimationState(),E!==w&&w.captureAnimationState()}function F(ce){return H("dragOverCompleted",{insertion:ce}),ce&&(c?u._hideClone():u._showClone(E),E!==w&&($(d,L?L.options.ghostClass:u.options.ghostClass,!1),$(d,s.ghostClass,!0)),L!==E&&E!==g.active?L=E:E===g.active&&L&&(L=null),w===E&&(E._ignoreWhileAnimating=o),E.animateAll(function(){H("dragOverAnimationComplete"),E._ignoreWhileAnimating=null}),E!==w&&(w.animateAll(),w._ignoreWhileAnimating=null)),(o===d&&!d.animated||o===n&&!o.animated)&&(Ce=null),!s.dragoverBubble&&!e.rootEl&&o!==document&&(d.parentNode[W]._isOutsideThisEl(e.target),!ce&&we(e)),!s.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),C=!0}function Y(){z=Z(d),he=Z(d,s.draggable),X({sortable:E,name:"change",toEl:n,newIndex:z,newDraggableIndex:he,originalEvent:e})}if(e.preventDefault!==void 0&&e.cancelable&&e.preventDefault(),o=ne(o,s.draggable,n,!0),H("dragOver"),g.eventCanceled)return C;if(d.contains(e.target)||o.animated&&o.animatingX&&o.animatingY||E._ignoreWhileAnimating===o)return F(!1);if(Qe=!1,u&&!s.disabled&&(c?f||(l=A!==O):L===this||(this.lastPutMode=Ve.checkPull(this,u,d,e))&&i.checkPut(this,u,d,e))){if(S=this._getDirection(e,o)==="vertical",r=M(d),H("dragOverValid"),g.eventCanceled)return C;if(l)return A=O,Q(),this._hideClone(),H("revert"),g.eventCanceled||(Ee?O.insertBefore(d,Ee):O.appendChild(d)),F(!0);var G=Ct(n,s.draggable);if(!G||Kn(e,S,this)&&!G.animated){if(G===d)return F(!1);if(G&&n===e.target&&(o=G),o&&(a=M(o)),$e(O,n,d,r,o,a,e,!!o)!==!1)return Q(),n.appendChild(d),A=n,Y(),F(!0)}else if(G&&Jn(e,S,this)){var ae=Te(n,0,s,!0);if(ae===d)return F(!1);if(o=ae,a=M(o),$e(O,n,d,r,o,a,e,!1)!==!1)return Q(),n.insertBefore(d,ae),A=n,Y(),F(!0)}else if(o.parentNode===n){a=M(o);var b=0,h,B=d.parentNode!==n,U=!qn(d.animated&&d.toRect||r,o.animated&&o.toRect||a,S),ie=S?"top":"left",te=Lt(o,"top","top")||Lt(d,"top","top"),de=te?te.scrollTop:void 0;Ce!==o&&(h=a[ie],Be=!1,He=!U&&s.invertSwap||B),b=Zn(e,o,a,S,U?1:s.swapThreshold,s.invertedSwapThreshold==null?s.swapThreshold:s.invertedSwapThreshold,He,Ce===o);var J;if(b!==0){var T=Z(d);do T-=b,J=A.children[T];while(J&&(m(J,"display")==="none"||J===_))}if(b===0||J===o)return F(!1);Ce=o,je=b;var I=o.nextElementSibling,q=!1;q=b===1;var De=$e(O,n,d,r,o,a,e,q);if(De!==!1)return(De===1||De===-1)&&(q=De===1),yt=!0,setTimeout(Gn,30),Q(),q&&!I?n.appendChild(d):o.parentNode.insertBefore(d,q?I:o),te&&Jt(te,0,de-te.scrollTop),A=d.parentNode,h!==void 0&&!He&&(Ge=Math.abs(h-M(o)[ie])),Y(),F(!0)}if(n.contains(d))return F(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){D(document,"mousemove",this._onTouchMove),D(document,"touchmove",this._onTouchMove),D(document,"pointermove",this._onTouchMove),D(document,"dragover",we),D(document,"mousemove",we),D(document,"touchmove",we)},_offUpEvents:function(){var e=this.el.ownerDocument;D(e,"mouseup",this._onDrop),D(e,"touchend",this._onDrop),D(e,"pointerup",this._onDrop),D(e,"touchcancel",this._onDrop),D(document,"selectstart",this)},_onDrop:function(e){var n=this.el,o=this.options;if(z=Z(d),he=Z(d,o.draggable),V("drop",this,{evt:e}),A=d&&d.parentNode,z=Z(d),he=Z(d,o.draggable),g.eventCanceled){this._nulling();return}xe=!1,He=!1,Be=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),_t(this.cloneId),_t(this._dragStartId),this.nativeDraggable&&(D(document,"drop",this),D(n,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),Fe&&m(document.body,"user-select",""),m(d,"transform",""),e&&(ke&&(e.cancelable&&e.preventDefault(),!o.dropBubble&&e.stopPropagation()),_&&_.parentNode&&_.parentNode.removeChild(_),(O===A||L&&L.lastPutMode!=="clone")&&k&&k.parentNode&&k.parentNode.removeChild(k),d&&(this.nativeDraggable&&D(d,"dragend",this),ht(d),d.style["will-change"]="",ke&&!xe&&$(d,L?L.options.ghostClass:this.options.ghostClass,!1),$(d,this.options.chosenClass,!1),X({sortable:this,name:"unchoose",toEl:A,newIndex:null,newDraggableIndex:null,originalEvent:e}),O!==A?(z>=0&&(X({rootEl:A,name:"add",toEl:A,fromEl:O,originalEvent:e}),X({sortable:this,name:"remove",toEl:A,originalEvent:e}),X({rootEl:A,name:"sort",toEl:A,fromEl:O,originalEvent:e}),X({sortable:this,name:"sort",toEl:A,originalEvent:e})),L&&L.save()):z!==Oe&&z>=0&&(X({sortable:this,name:"update",toEl:A,originalEvent:e}),X({sortable:this,name:"sort",toEl:A,originalEvent:e})),g.active&&((z==null||z===-1)&&(z=Oe,he=Re),X({sortable:this,name:"end",toEl:A,originalEvent:e}),this.save()))),this._nulling()},_nulling:function(){V("nulling",this),O=d=A=_=Ee=k=We=pe=_e=ee=ke=z=he=Oe=Re=Ce=je=L=Ve=g.dragged=g.ghost=g.clone=g.active=null,tt.forEach(function(e){e.checked=!0}),tt.length=dt=ct=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":d&&(this._onDragOver(e),Wn(e));break;case"selectstart":e.preventDefault();break}},toArray:function(){for(var e=[],n,o=this.el.children,r=0,a=o.length,l=this.options;r<a;r++)n=o[r],ne(n,l.draggable,this.el,!1)&&e.push(n.getAttribute(l.dataIdAttr)||eo(n));return e},sort:function(e,n){var o={},r=this.el;this.toArray().forEach(function(a,l){var s=r.children[l];ne(s,this.options.draggable,r,!1)&&(o[a]=s)},this),n&&this.captureAnimationState(),e.forEach(function(a){o[a]&&(r.removeChild(o[a]),r.appendChild(o[a]))}),n&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,n){return ne(e,n||this.options.draggable,this.el,!1)},option:function(e,n){var o=this.options;if(n===void 0)return o[e];var r=Xe.modifyOption(this,e,n);typeof r<"u"?o[e]=r:o[e]=n,e==="group"&&en(o)},destroy:function(){V("destroy",this);var e=this.el;e[W]=null,D(e,"mousedown",this._onTapStart),D(e,"touchstart",this._onTapStart),D(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(D(e,"dragover",this),D(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),function(n){n.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),et.splice(et.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!pe){if(V("hideClone",this),g.eventCanceled)return;m(k,"display","none"),this.options.removeCloneOnHide&&k.parentNode&&k.parentNode.removeChild(k),pe=!0}},_showClone:function(e){if(e.lastPutMode!=="clone"){this._hideClone();return}if(pe){if(V("showClone",this),g.eventCanceled)return;d.parentNode==O&&!this.options.group.revertClone?O.insertBefore(k,d):Ee?O.insertBefore(k,Ee):O.appendChild(k),this.options.group.revertClone&&this.animate(d,k),m(k,"display",""),pe=!1}}};function Wn(t){t.dataTransfer&&(t.dataTransfer.dropEffect="move"),t.cancelable&&t.preventDefault()}function $e(t,e,n,o,r,a,l,s){var i,u=t[W],c=u.options.onMove,f;return window.CustomEvent&&!ue&&!Ue?i=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(i=document.createEvent("Event"),i.initEvent("move",!0,!0)),i.to=e,i.from=t,i.dragged=n,i.draggedRect=o,i.related=r||e,i.relatedRect=a||M(e),i.willInsertAfter=s,i.originalEvent=l,t.dispatchEvent(i),c&&(f=c.call(u,i,l)),f}function ht(t){t.draggable=!1}function Gn(){yt=!1}function Jn(t,e,n){var o=M(Te(n.el,0,n.options,!0)),r=10;return e?t.clientX<o.left-r||t.clientY<o.top&&t.clientX<o.right:t.clientY<o.top-r||t.clientY<o.bottom&&t.clientX<o.left}function Kn(t,e,n){var o=M(Ct(n.el,n.options.draggable)),r=10;return e?t.clientX>o.right+r||t.clientX<=o.right&&t.clientY>o.bottom&&t.clientX>=o.left:t.clientX>o.right&&t.clientY>o.top||t.clientX<=o.right&&t.clientY>o.bottom+r}function Zn(t,e,n,o,r,a,l,s){var i=o?t.clientY:t.clientX,u=o?n.height:n.width,c=o?n.top:n.left,f=o?n.bottom:n.right,w=!1;if(!l){if(s&&Ge<u*r){if(!Be&&(je===1?i>c+u*a/2:i<f-u*a/2)&&(Be=!0),Be)w=!0;else if(je===1?i<c+Ge:i>f-Ge)return-je}else if(i>c+u*(1-r)/2&&i<f-u*(1-r)/2)return Qn(e)}return w=w||l,w&&(i<c+u*a/2||i>f-u*a/2)?i>c+u/2?1:-1:0}function Qn(t){return Z(d)<Z(t)?1:-1}function eo(t){for(var e=t.tagName+t.className+t.src+t.href+t.textContent,n=e.length,o=0;n--;)o+=e.charCodeAt(n);return o.toString(36)}function to(t){tt.length=0;for(var e=t.getElementsByTagName("input"),n=e.length;n--;){var o=e[n];o.checked&&tt.push(o)}}function Je(t){return setTimeout(t,0)}function _t(t){return clearTimeout(t)}ot&&x(document,"touchmove",function(t){(g.active||xe)&&t.cancelable&&t.preventDefault()});g.utils={on:x,off:D,css:m,find:Wt,is:function(e,n){return!!ne(e,n,e,!1)},extend:Rn,throttle:Gt,closest:ne,toggleClass:$,clone:Kt,index:Z,nextTick:Je,cancelNextTick:_t,detectDirection:Qt,getChild:Te};g.get=function(t){return t[W]};g.mount=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];e[0].constructor===Array&&(e=e[0]),e.forEach(function(o){if(!o.prototype||!o.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(o));o.utils&&(g.utils=re(re({},g.utils),o.utils)),Xe.mount(o)})};g.create=function(t,e){return new g(t,e)};g.version=Nn;var N=[],Ne,wt,Et=!1,pt,mt,nt,Me;function no(){function t(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var e in this)e.charAt(0)==="_"&&typeof this[e]=="function"&&(this[e]=this[e].bind(this))}return t.prototype={dragStarted:function(n){var o=n.originalEvent;this.sortable.nativeDraggable?x(document,"dragover",this._handleAutoScroll):this.options.supportPointer?x(document,"pointermove",this._handleFallbackAutoScroll):o.touches?x(document,"touchmove",this._handleFallbackAutoScroll):x(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(n){var o=n.originalEvent;!this.options.dragOverBubble&&!o.rootEl&&this._handleAutoScroll(o)},drop:function(){this.sortable.nativeDraggable?D(document,"dragover",this._handleAutoScroll):(D(document,"pointermove",this._handleFallbackAutoScroll),D(document,"touchmove",this._handleFallbackAutoScroll),D(document,"mousemove",this._handleFallbackAutoScroll)),Xt(),Ke(),jn()},nulling:function(){nt=wt=Ne=Et=Me=pt=mt=null,N.length=0},_handleFallbackAutoScroll:function(n){this._handleAutoScroll(n,!0)},_handleAutoScroll:function(n,o){var r=this,a=(n.touches?n.touches[0]:n).clientX,l=(n.touches?n.touches[0]:n).clientY,s=document.elementFromPoint(a,l);if(nt=n,o||this.options.forceAutoScrollFallback||Ue||ue||Fe){gt(n,this.options,s,o);var i=me(s,!0);Et&&(!Me||a!==pt||l!==mt)&&(Me&&Xt(),Me=setInterval(function(){var u=me(document.elementFromPoint(a,l),!0);u!==i&&(i=u,Ke()),gt(n,r.options,u,o)},10),pt=a,mt=l)}else{if(!this.options.bubbleScroll||me(s,!0)===oe()){Ke();return}gt(n,this.options,me(s,!1),!1)}}},se(t,{pluginName:"scroll",initializeByDefault:!0})}function Ke(){N.forEach(function(t){clearInterval(t.pid)}),N=[]}function Xt(){clearInterval(Me)}var gt=Gt(function(t,e,n,o){if(e.scroll){var r=(t.touches?t.touches[0]:t).clientX,a=(t.touches?t.touches[0]:t).clientY,l=e.scrollSensitivity,s=e.scrollSpeed,i=oe(),u=!1,c;wt!==n&&(wt=n,Ke(),Ne=e.scroll,c=e.scrollFn,Ne===!0&&(Ne=me(n,!0)));var f=0,w=Ne;do{var S=w,E=M(S),C=E.top,H=E.bottom,Q=E.left,F=E.right,Y=E.width,G=E.height,ae=void 0,b=void 0,h=S.scrollWidth,B=S.scrollHeight,U=m(S),ie=S.scrollLeft,te=S.scrollTop;S===i?(ae=Y<h&&(U.overflowX==="auto"||U.overflowX==="scroll"||U.overflowX==="visible"),b=G<B&&(U.overflowY==="auto"||U.overflowY==="scroll"||U.overflowY==="visible")):(ae=Y<h&&(U.overflowX==="auto"||U.overflowX==="scroll"),b=G<B&&(U.overflowY==="auto"||U.overflowY==="scroll"));var de=ae&&(Math.abs(F-r)<=l&&ie+Y<h)-(Math.abs(Q-r)<=l&&!!ie),J=b&&(Math.abs(H-a)<=l&&te+G<B)-(Math.abs(C-a)<=l&&!!te);if(!N[f])for(var T=0;T<=f;T++)N[T]||(N[T]={});(N[f].vx!=de||N[f].vy!=J||N[f].el!==S)&&(N[f].el=S,N[f].vx=de,N[f].vy=J,clearInterval(N[f].pid),(de!=0||J!=0)&&(u=!0,N[f].pid=setInterval((function(){o&&this.layer===0&&g.active._onTouchMove(nt);var I=N[this.layer].vy?N[this.layer].vy*s:0,q=N[this.layer].vx?N[this.layer].vx*s:0;typeof c=="function"&&c.call(g.dragged.parentNode[W],q,I,t,nt,N[this.layer].el)!=="continue"||Jt(N[this.layer].el,q,I)}).bind({layer:f}),24))),f++}while(e.bubbleScroll&&w!==i&&(w=me(w,!1)));Et=u}},30),on=function(e){var n=e.originalEvent,o=e.putSortable,r=e.dragEl,a=e.activeSortable,l=e.dispatchSortableEvent,s=e.hideGhostForTarget,i=e.unhideGhostForTarget;if(n){var u=o||a;s();var c=n.changedTouches&&n.changedTouches.length?n.changedTouches[0]:n,f=document.elementFromPoint(c.clientX,c.clientY);i(),u&&!u.el.contains(f)&&(l("spill"),this.onSpill({dragEl:r,putSortable:o}))}};function xt(){}xt.prototype={startIndex:null,dragStart:function(e){var n=e.oldDraggableIndex;this.startIndex=n},onSpill:function(e){var n=e.dragEl,o=e.putSortable;this.sortable.captureAnimationState(),o&&o.captureAnimationState();var r=Te(this.sortable.el,this.startIndex,this.options);r?this.sortable.el.insertBefore(n,r):this.sortable.el.appendChild(n),this.sortable.animateAll(),o&&o.animateAll()},drop:on};se(xt,{pluginName:"revertOnSpill"});function Ot(){}Ot.prototype={onSpill:function(e){var n=e.dragEl,o=e.putSortable,r=o||this.sortable;r.captureAnimationState(),n.parentNode&&n.parentNode.removeChild(n),r.animateAll()},drop:on};se(Ot,{pluginName:"removeOnSpill"});g.mount(new no);g.mount(Ot,xt);function vt(t){t.parentElement!==null&&t.parentElement.removeChild(t)}function Yt(t,e,n){const o=n===0?t.children[0]:t.children[n-1].nextSibling;t.insertBefore(e,o)}function oo(){return typeof window<"u"?window.console:global.console}const ro=oo();function ao(t){const e=Object.create(null);return function(o){return e[o]||(e[o]=t(o))}}const io=/-(\w)/g,lo=ao(t=>t.replace(io,(e,n)=>n.toUpperCase())),rn=["Start","Add","Remove","Update","End"],an=["Choose","Unchoose","Sort","Filter","Clone"],ln=["Move"],so=[ln,rn,an].flatMap(t=>t).map(t=>`on${t}`),Dt={manage:ln,manageAndEmit:rn,emit:an};function uo(t){return so.indexOf(t)!==-1}const co=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","label","legend","li","link","main","map","mark","math","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rb","rp","rt","rtc","ruby","s","samp","script","section","select","slot","small","source","span","strong","style","sub","summary","sup","svg","table","tbody","td","template","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr"];function fo(t){return co.includes(t)}function ho(t){return["transition-group","TransitionGroup"].includes(t)}function sn(t){return["id","class","role","style"].includes(t)||t.startsWith("data-")||t.startsWith("aria-")||t.startsWith("on")}function un(t){return t.reduce((e,[n,o])=>(e[n]=o,e),{})}function po({$attrs:t,componentData:e={}}){return{...un(Object.entries(t).filter(([o,r])=>sn(o))),...e}}function mo({$attrs:t,callBackBuilder:e}){const n=un(dn(t));Object.entries(e).forEach(([r,a])=>{Dt[r].forEach(l=>{n[`on${l}`]=a(l)})});const o=`[data-draggable]${n.draggable||""}`;return{...n,draggable:o}}function dn(t){return Object.entries(t).filter(([e,n])=>!sn(e)).map(([e,n])=>[lo(e),n]).filter(([e,n])=>!uo(e))}const Vt=({el:t})=>t,go=(t,e)=>t.__draggable_context=e,Ht=t=>t.__draggable_context;class vo{constructor({nodes:{header:e,default:n,footer:o},root:r,realList:a}){this.defaultNodes=n,this.children=[...e,...n,...o],this.externalComponent=r.externalComponent,this.rootTransition=r.transition,this.tag=r.tag,this.realList=a}get _isRootComponent(){return this.externalComponent||this.rootTransition}render(e,n){const{tag:o,children:r,_isRootComponent:a}=this;return e(o,n,a?{default:()=>r}:r)}updated(){const{defaultNodes:e,realList:n}=this;e.forEach((o,r)=>{go(Vt(o),{element:n[r],index:r})})}getUnderlyingVm(e){return Ht(e)}getVmIndexFromDomIndex(e,n){const{defaultNodes:o}=this,{length:r}=o,a=n.children,l=a.item(e);if(l===null)return r;const s=Ht(l);if(s)return s.index;if(r===0)return 0;const i=Vt(o[0]),u=[...a].findIndex(c=>c===i);return e<u?0:r}}function bo(t,e){const n=t[e];return n?n():[]}function yo({$slots:t,realList:e,getKey:n}){const o=e||[],[r,a]=["header","footer"].map(i=>bo(t,i)),{item:l}=t;if(!l)throw new Error("draggable element must have an item slot");const s=o.flatMap((i,u)=>l({element:i,index:u}).map(c=>(c.key=n(i),c.props={...c.props||{},"data-draggable":!0},c)));if(s.length!==o.length)throw new Error("Item slot must have only one child");return{header:r,footer:a,default:s}}function _o(t){const e=ho(t),n=!fo(t)&&!e;return{transition:e,externalComponent:n,tag:n?j(t):e?gn:t}}function wo({$slots:t,tag:e,realList:n,getKey:o}){const r=yo({$slots:t,realList:n,getKey:o}),a=_o(e);return new vo({nodes:r,root:a,realList:n})}function cn(t,e){qt(()=>this.$emit(t.toLowerCase(),e))}function fn(t){return(e,n)=>{if(this.realList!==null)return this[`onDrag${t}`](e,n)}}function Eo(t){const e=fn.call(this,t);return(n,o)=>{e.call(this,n,o),cn.call(this,t,n)}}let bt=null;const Do={list:{type:Array,required:!1,default:null},modelValue:{type:Array,required:!1,default:null},itemKey:{type:[String,Function],required:!0},clone:{type:Function,default:t=>t},tag:{type:String,default:"div"},move:{type:Function,default:null},componentData:{type:Object,required:!1,default:null}},So=["update:modelValue","change",...[...Dt.manageAndEmit,...Dt.emit].map(t=>t.toLowerCase())],Co=vn({name:"draggable",inheritAttrs:!1,props:Do,emits:So,data(){return{error:!1}},render(){try{this.error=!1;const{$slots:t,$attrs:e,tag:n,componentData:o,realList:r,getKey:a}=this,l=wo({$slots:t,tag:n,realList:r,getKey:a});this.componentStructure=l;const s=po({$attrs:e,componentData:o});return l.render(Pt,s)}catch(t){return this.error=!0,Pt("pre",{style:{color:"red"}},t.stack)}},created(){this.list!==null&&this.modelValue!==null&&ro.error("modelValue and list props are mutually exclusive! Please set one or another.")},mounted(){if(this.error)return;const{$attrs:t,$el:e,componentStructure:n}=this;n.updated();const o=mo({$attrs:t,callBackBuilder:{manageAndEmit:a=>Eo.call(this,a),emit:a=>cn.bind(this,a),manage:a=>fn.call(this,a)}}),r=e.nodeType===1?e:e.parentElement;this._sortable=new g(r,o),this.targetDomElement=r,r.__draggable_component__=this},updated(){this.componentStructure.updated()},beforeUnmount(){this._sortable!==void 0&&this._sortable.destroy()},computed:{realList(){const{list:t}=this;return t||this.modelValue},getKey(){const{itemKey:t}=this;return typeof t=="function"?t:e=>e[t]}},watch:{$attrs:{handler(t){const{_sortable:e}=this;e&&dn(t).forEach(([n,o])=>{e.option(n,o)})},deep:!0}},methods:{getUnderlyingVm(t){return this.componentStructure.getUnderlyingVm(t)||null},getUnderlyingPotencialDraggableComponent(t){return t.__draggable_component__},emitChanges(t){qt(()=>this.$emit("change",t))},alterList(t){if(this.list){t(this.list);return}const e=[...this.modelValue];t(e),this.$emit("update:modelValue",e)},spliceList(){const t=e=>e.splice(...arguments);this.alterList(t)},updatePosition(t,e){const n=o=>o.splice(e,0,o.splice(t,1)[0]);this.alterList(n)},getRelatedContextFromMoveEvent({to:t,related:e}){const n=this.getUnderlyingPotencialDraggableComponent(t);if(!n)return{component:n};const o=n.realList,r={list:o,component:n};return t!==e&&o?{...n.getUnderlyingVm(e)||{},...r}:r},getVmIndexFromDomIndex(t){return this.componentStructure.getVmIndexFromDomIndex(t,this.targetDomElement)},onDragStart(t){this.context=this.getUnderlyingVm(t.item),t.item._underlying_vm_=this.clone(this.context.element),bt=t.item},onDragAdd(t){const e=t.item._underlying_vm_;if(e===void 0)return;vt(t.item);const n=this.getVmIndexFromDomIndex(t.newIndex);this.spliceList(n,0,e);const o={element:e,newIndex:n};this.emitChanges({added:o})},onDragRemove(t){if(Yt(this.$el,t.item,t.oldIndex),t.pullMode==="clone"){vt(t.clone);return}const{index:e,element:n}=this.context;this.spliceList(e,1);const o={element:n,oldIndex:e};this.emitChanges({removed:o})},onDragUpdate(t){vt(t.item),Yt(t.from,t.item,t.oldIndex);const e=this.context.index,n=this.getVmIndexFromDomIndex(t.newIndex);this.updatePosition(e,n);const o={element:this.context.element,oldIndex:e,newIndex:n};this.emitChanges({moved:o})},computeFutureIndex(t,e){if(!t.element)return 0;const n=[...e.to.children].filter(l=>l.style.display!=="none"),o=n.indexOf(e.related),r=t.component.getVmIndexFromDomIndex(o);return n.indexOf(bt)!==-1||!e.willInsertAfter?r:r+1},onDragMove(t,e){const{move:n,realList:o}=this;if(!n||!o)return!0;const r=this.getRelatedContextFromMoveEvent(t),a=this.computeFutureIndex(r,t),l={...this.context,futureIndex:a},s={...t,relatedContext:r,draggedContext:l};return n(s,e)},onDragEnd(){bt=null}}});const xo=t=>(Sn("data-v-116ae8ea"),t=t(),Cn(),t),Oo={class:"json-schema-editor"},Io={class:"editor-mode-switch"},To={key:0,class:"visual-editor"},Po={class:"properties-list"},Ao={class:"properties-header"},ko=xo(()=>fe("h3",null,"参数列表",-1)),No={class:"property-item"},Mo={class:"property-header"},Fo={class:"property-name"},Lo={class:"property-actions"},Ro={key:0,class:"property-description"},jo={key:1,class:"json-editor"},Bo={__name:"JsonSchemaEditor",props:{value:{type:String,required:!0}},emits:["update:value"],setup(t,{emit:e}){const n=t,o=ve("visual"),r=ve(n.value),a=ve([]),l=ve([]),s=ve(!1),i=ve({key:"",schema:{type:"string",description:""},required:!1}),u=ve(-1),c=yn(()=>u.value===-1);_n(()=>{f()}),At(()=>n.value,b=>{b!==r.value&&(r.value=b,o.value==="visual"&&f())},{deep:!0});const f=()=>{try{const b=JSON.parse(r.value);if(a.value=[],b.properties)for(const[h,B]of Object.entries(b.properties))a.value.push({key:h,schema:B});l.value=b.required||[]}catch(b){at.error("JSON格式错误: "+b.message)}},w=()=>{const b={type:"object",properties:{},required:[...l.value]};for(const h of a.value)b.properties[h.key]={...h.schema};return b},S=()=>{const b=w();r.value=JSON.stringify(b,null,2),e("update:value",r.value)},E=()=>{e("update:value",r.value),o.value==="visual"&&f()},C=()=>{i.value={key:"",schema:{type:"string",description:""},required:!1},u.value=-1,s.value=!0},H=b=>{const h=a.value[b];i.value={key:h.key,schema:{...h.schema},required:Y(h.key)},u.value=b,s.value=!0},Q=b=>{const h=a.value[b].key,B=l.value.indexOf(h);B!==-1&&l.value.splice(B,1),a.value.splice(b,1),S()},F=()=>{if(!i.value.key){at.error("参数名称不能为空");return}if(c.value&&a.value.some(B=>B.key===i.value.key)){at.error("参数名称已存在");return}i.value.schema.type==="array"&&!i.value.schema.items&&(i.value.schema.items={type:"string"}),c.value?a.value.push({key:i.value.key,schema:{...i.value.schema}}):a.value[u.value].schema={...i.value.schema};const b=l.value.indexOf(i.value.key);i.value.required&&b===-1?l.value.push(i.value.key):!i.value.required&&b!==-1&&l.value.splice(b,1),S(),s.value=!1},Y=b=>l.value.includes(b),G=b=>({string:"字符串",number:"数字",integer:"整数",boolean:"布尔值",array:"数组",object:"对象"})[b]||b,ae=()=>{S()};return At(o,b=>{b==="visual"&&f()}),(b,h)=>{const B=j("a-radio-button"),U=j("a-radio-group"),ie=j("a-button"),te=j("a-empty"),de=j("a-tag"),J=j("a-input"),T=j("a-form-item"),I=j("a-select-option"),q=j("a-select"),De=j("a-textarea"),ce=j("a-switch"),ge=j("a-input-number"),It=j("a-alert"),Ye=j("a-form"),rt=j("a-modal");return K(),be("div",Oo,[fe("div",Io,[p(U,{value:o.value,"onUpdate:value":h[0]||(h[0]=y=>o.value=y),"button-style":"solid"},{default:v(()=>[p(B,{value:"visual"},{default:v(()=>[P("图形化编辑")]),_:1}),p(B,{value:"json"},{default:v(()=>[P("JSON编辑")]),_:1})]),_:1},8,["value"])]),o.value==="visual"?(K(),be("div",To,[fe("div",Po,[fe("div",Ao,[ko,p(ie,{type:"primary",size:"small",onClick:C},{icon:v(()=>[p(Pe(wn))]),default:v(()=>[P(" 添加参数 ")]),_:1})]),a.value.length?(K(),Ae(Pe(Co),{key:1,modelValue:a.value,"onUpdate:modelValue":h[1]||(h[1]=y=>a.value=y),"item-key":"key",handle:".drag-handle","ghost-class":"ghost",onEnd:ae},{item:v(({element:y,index:Tt})=>[fe("div",No,[fe("div",Mo,[p(Pe(Tn),{class:"drag-handle"}),fe("span",Fo,it(y.key),1),p(de,{color:"blue"},{default:v(()=>[P(it(G(y.schema.type)),1)]),_:2},1024),Y(y.key)?(K(),Ae(de,{key:0,color:"red"},{default:v(()=>[P("必填")]),_:1})):ye("",!0),fe("div",Lo,[p(ie,{type:"text",size:"small",onClick:hn=>H(Tt)},{icon:v(()=>[p(Pe(En))]),_:2},1032,["onClick"]),p(ie,{type:"text",size:"small",onClick:hn=>Q(Tt)},{icon:v(()=>[p(Pe(Dn))]),_:2},1032,["onClick"])])]),y.schema.description?(K(),be("div",Ro,it(y.schema.description),1)):ye("",!0)])]),_:1},8,["modelValue"])):(K(),Ae(te,{key:0,description:"暂无参数定义"}))])])):(K(),be("div",jo,[p(pn,{value:r.value,"onUpdate:value":h[2]||(h[2]=y=>r.value=y),language:"json",options:{automaticLayout:!0,scrollBeyondLastLine:!1},onChange:E},null,8,["value"])])),p(rt,{open:s.value,"onUpdate:open":h[17]||(h[17]=y=>s.value=y),title:c.value?"添加参数":"编辑参数",onOk:F,onCancel:h[18]||(h[18]=y=>s.value=!1)},{default:v(()=>[p(Ye,{model:i.value,layout:"vertical"},{default:v(()=>[p(T,{label:"参数名称",required:""},{default:v(()=>[p(J,{value:i.value.key,"onUpdate:value":h[3]||(h[3]=y=>i.value.key=y),placeholder:"请输入参数名称",disabled:!c.value},null,8,["value","disabled"])]),_:1}),p(T,{label:"参数类型",required:""},{default:v(()=>[p(q,{value:i.value.schema.type,"onUpdate:value":h[4]||(h[4]=y=>i.value.schema.type=y),placeholder:"请选择参数类型"},{default:v(()=>[p(I,{value:"string"},{default:v(()=>[P("字符串 (string)")]),_:1}),p(I,{value:"number"},{default:v(()=>[P("数字 (number)")]),_:1}),p(I,{value:"integer"},{default:v(()=>[P("整数 (integer)")]),_:1}),p(I,{value:"boolean"},{default:v(()=>[P("布尔值 (boolean)")]),_:1}),p(I,{value:"array"},{default:v(()=>[P("数组 (array)")]),_:1}),p(I,{value:"object"},{default:v(()=>[P("对象 (object)")]),_:1})]),_:1},8,["value"])]),_:1}),p(T,{label:"描述"},{default:v(()=>[p(De,{value:i.value.schema.description,"onUpdate:value":h[5]||(h[5]=y=>i.value.schema.description=y),placeholder:"请输入参数描述",rows:2},null,8,["value"])]),_:1}),p(T,{label:"是否必填"},{default:v(()=>[p(ce,{checked:i.value.required,"onUpdate:checked":h[6]||(h[6]=y=>i.value.required=y)},null,8,["checked"])]),_:1}),i.value.schema.type==="string"?(K(),be(lt,{key:0},[p(T,{label:"默认值"},{default:v(()=>[p(J,{value:i.value.schema.default,"onUpdate:value":h[7]||(h[7]=y=>i.value.schema.default=y),placeholder:"请输入默认值"},null,8,["value"])]),_:1}),p(T,{label:"格式"},{default:v(()=>[p(q,{value:i.value.schema.format,"onUpdate:value":h[8]||(h[8]=y=>i.value.schema.format=y),placeholder:"请选择格式",allowClear:""},{default:v(()=>[p(I,{value:"email"},{default:v(()=>[P("邮箱 (email)")]),_:1}),p(I,{value:"uri"},{default:v(()=>[P("URI (uri)")]),_:1}),p(I,{value:"date"},{default:v(()=>[P("日期 (date)")]),_:1}),p(I,{value:"date-time"},{default:v(()=>[P("日期时间 (date-time)")]),_:1}),p(I,{value:"password"},{default:v(()=>[P("密码 (password)")]),_:1}),p(I,{value:"textarea"},{default:v(()=>[P("文本域 (textarea)")]),_:1})]),_:1},8,["value"])]),_:1}),p(T,{label:"枚举值"},{default:v(()=>[p(q,{value:i.value.schema.enum,"onUpdate:value":h[9]||(h[9]=y=>i.value.schema.enum=y),mode:"tags",placeholder:"输入后按回车添加枚举值",tokenSeparators:[","]},null,8,["value"])]),_:1})],64)):ye("",!0),i.value.schema.type==="number"||i.value.schema.type==="integer"?(K(),be(lt,{key:1},[p(T,{label:"默认值"},{default:v(()=>[p(ge,{value:i.value.schema.default,"onUpdate:value":h[10]||(h[10]=y=>i.value.schema.default=y),placeholder:"请输入默认值",precision:i.value.schema.type==="integer"?0:void 0,style:{width:"100%"}},null,8,["value","precision"])]),_:1}),p(T,{label:"最小值"},{default:v(()=>[p(ge,{value:i.value.schema.minimum,"onUpdate:value":h[11]||(h[11]=y=>i.value.schema.minimum=y),placeholder:"请输入最小值",precision:i.value.schema.type==="integer"?0:void 0,style:{width:"100%"}},null,8,["value","precision"])]),_:1}),p(T,{label:"最大值"},{default:v(()=>[p(ge,{value:i.value.schema.maximum,"onUpdate:value":h[12]||(h[12]=y=>i.value.schema.maximum=y),placeholder:"请输入最大值",precision:i.value.schema.type==="integer"?0:void 0,style:{width:"100%"}},null,8,["value","precision"])]),_:1})],64)):ye("",!0),i.value.schema.type==="boolean"?(K(),Ae(T,{key:2,label:"默认值"},{default:v(()=>[p(ce,{checked:i.value.schema.default,"onUpdate:checked":h[13]||(h[13]=y=>i.value.schema.default=y)},null,8,["checked"])]),_:1})):ye("",!0),i.value.schema.type==="array"?(K(),be(lt,{key:3},[p(T,{label:"数组元素类型",required:""},{default:v(()=>[p(q,{value:i.value.schema.items.type,"onUpdate:value":h[14]||(h[14]=y=>i.value.schema.items.type=y),placeholder:"请选择数组元素类型"},{default:v(()=>[p(I,{value:"string"},{default:v(()=>[P("字符串 (string)")]),_:1}),p(I,{value:"number"},{default:v(()=>[P("数字 (number)")]),_:1}),p(I,{value:"integer"},{default:v(()=>[P("整数 (integer)")]),_:1}),p(I,{value:"boolean"},{default:v(()=>[P("布尔值 (boolean)")]),_:1}),p(I,{value:"object"},{default:v(()=>[P("对象 (object)")]),_:1})]),_:1},8,["value"])]),_:1}),p(T,{label:"最小元素数量"},{default:v(()=>[p(ge,{value:i.value.schema.minItems,"onUpdate:value":h[15]||(h[15]=y=>i.value.schema.minItems=y),placeholder:"请输入最小元素数量",min:0,precision:0,style:{width:"100%"}},null,8,["value"])]),_:1}),p(T,{label:"最大元素数量"},{default:v(()=>[p(ge,{value:i.value.schema.maxItems,"onUpdate:value":h[16]||(h[16]=y=>i.value.schema.maxItems=y),placeholder:"请输入最大元素数量",min:0,precision:0,style:{width:"100%"}},null,8,["value"])]),_:1})],64)):ye("",!0),i.value.schema.type==="object"?(K(),Ae(It,{key:4,message:"对象类型提示",description:"对象类型参数可以包含嵌套属性，保存后可以在图形化编辑器中单独编辑其属性。",type:"info","show-icon":"",style:{"margin-bottom":"16px"}})):ye("",!0)]),_:1},8,["model"])]),_:1},8,["open","title"])])}}},Yo=bn(Bo,[["__scopeId","data-v-116ae8ea"]]);export{Yo as J};
