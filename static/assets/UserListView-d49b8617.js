import{u as T,r as i,o as A,b as r,c as m,d as C,w as e,e as a,h as D,f as s,P as U,g as h,k as w,t as x,E as I,s as L,v as R,l as j}from"./index-94aa741e.js";import{c as S,A as q,f as F}from"./AppLayout-b20be4c8.js";const G={class:"action-bar"},H=["onClick"],J={key:1},X={__name:"UserListView",setup(K){const d=T(),v=i(!1),f=i([]),g=i(0),u=i(1),p=i(10),k=i(""),z=[{title:"用户名",dataIndex:"username",key:"username",width:160,ellipsis:!0,tooltip:!0},{title:"邮箱",dataIndex:"email",key:"email",width:160,ellipsis:!0,tooltip:!0},{title:"创建时间",key:"created_at",width:160,ellipsis:!0,tooltip:!0},{title:"操作",key:"action",width:200,fixed:"right"}];A(()=>{c()});const c=async()=>{v.value=!0;try{await S({method:"get",url:"/api/v1/user",params:{page:u.value,size:p.value,search:k.value||void 0},onSuccess:(o,t)=>{f.value=o,g.value=t.total},errorMessage:"获取用户列表失败"})}finally{v.value=!1}},$=()=>{u.value=1,c()},b=(o,t)=>{u.value=o,p.value=t,c()},E=(o,t)=>{u.value=1,p.value=t,c()},O=async o=>{try{await S({method:"delete",url:`/api/v1/user/${o}`,successMessage:"删除成功",errorMessage:"删除失败"}),c()}catch(t){console.error("Error deleting user:",t)}};return(o,t)=>{const P=r("a-input-search"),_=r("a-button"),V=r("a-popconfirm"),B=r("a-space"),M=r("a-table"),N=r("a-card");return m(),C(q,{"current-page-key":"user"},{default:e(()=>[a(N,{title:"用户管理"},{default:e(()=>[D("div",G,[a(P,{value:k.value,"onUpdate:value":t[0]||(t[0]=n=>k.value=n),placeholder:"搜索用户名或邮箱",style:{width:"300px"},onSearch:$},null,8,["value"]),a(_,{type:"primary",onClick:t[1]||(t[1]=n=>s(d).push("/user/create"))},{icon:e(()=>[a(s(U))]),default:e(()=>[h(" 创建用户 ")]),_:1})]),a(M,{columns:z,"data-source":f.value,loading:v.value,pagination:{current:u.value,pageSize:p.value,total:g.value,onChange:b,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:E,showTotal:n=>`共 ${n} 条记录`},"row-key":n=>n.id},{bodyCell:e(({column:n,record:l})=>[n.key==="username"?(m(),w("a",{key:0,onClick:y=>s(d).push(`/user/${l.id}`)},x(l.username),9,H)):n.key==="created_at"?(m(),w("span",J,x(s(F)(l.created_at)),1)):n.key==="action"?(m(),C(B,{key:2},{default:e(()=>[a(_,{type:"link",size:"small",onClick:y=>s(d).push(`/user/${l.id}`)},{icon:e(()=>[a(s(I))]),default:e(()=>[h(" 查看 ")]),_:2},1032,["onClick"]),a(_,{type:"link",size:"small",onClick:y=>s(d).push(`/user/${l.id}/edit`)},{icon:e(()=>[a(s(L))]),default:e(()=>[h(" 编辑 ")]),_:2},1032,["onClick"]),a(V,{title:"确定要删除此用户吗？","ok-text":"确定","cancel-text":"取消",onConfirm:y=>O(l.id)},{default:e(()=>[a(_,{type:"link",size:"small",danger:""},{icon:e(()=>[a(s(R))]),default:e(()=>[h(" 删除 ")]),_:1})]),_:2},1032,["onConfirm"])]),_:2},1024)):j("",!0)]),_:1},8,["data-source","loading","pagination","row-key"])]),_:1})]),_:1})}}};export{X as default};
