import{e as l,A as $e,_ as Ce,u as xe,x as Oe,y as Z,r as d,o as Se,b as f,c as o,d as m,w as s,h as t,t as i,g,l as h,f as p,s as Me,k as c,F as P,z as D,p as Ve,m as Be,q as ee}from"./index-94aa741e.js";import{c as k,A as Pe,f as U,S as De,F as Ee}from"./AppLayout-b20be4c8.js";/* empty css                                                     */import{R as te}from"./RollbackOutlined-27c22304.js";import{P as Te}from"./PauseCircleOutlined-24b40f6c.js";import{P as Ae}from"./PlayCircleOutlined-5272f431.js";var Ne={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M304 280h56c4.4 0 8-3.6 8-8 0-28.3 5.9-53.2 17.1-73.5 10.6-19.4 26-34.8 45.4-45.4C450.9 142 475.7 136 504 136h16c28.3 0 53.2 5.9 73.5 17.1 19.4 10.6 34.8 26 45.4 45.4C650 218.9 656 243.7 656 272c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8 0-40-8.8-76.7-25.9-108.1a184.31 184.31 0 00-74-74C596.7 72.8 560 64 520 64h-16c-40 0-76.7 8.8-108.1 25.9a184.31 184.31 0 00-74 74C304.8 195.3 296 232 296 272c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M940 512H792V412c76.8 0 139-62.2 139-139 0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8a63 63 0 01-63 63H232a63 63 0 01-63-63c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8 0 76.8 62.2 139 139 139v100H84c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h148v96c0 6.5.2 13 .7 19.3C164.1 728.6 116 796.7 116 876c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8 0-44.2 23.9-82.9 59.6-103.7a273 273 0 0022.7 49c24.3 41.5 59 76.2 100.5 100.5S460.5 960 512 960s99.8-13.9 141.3-38.2a281.38 281.38 0 00123.2-149.5A120 120 0 01836 876c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8 0-79.3-48.1-147.4-116.7-176.7.4-6.4.7-12.8.7-19.3v-96h148c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM716 680c0 36.8-9.7 72-27.8 102.9-17.7 30.3-43 55.6-73.3 73.3C584 874.3 548.8 884 512 884s-72-9.7-102.9-27.8c-30.3-17.7-55.6-43-73.3-73.3A202.75 202.75 0 01308 680V412h408v268z"}}]},name:"bug",theme:"outlined"};const Re=Ne;function se(y){for(var _=1;_<arguments.length;_++){var b=arguments[_]!=null?Object(arguments[_]):{},v=Object.keys(b);typeof Object.getOwnPropertySymbols=="function"&&(v=v.concat(Object.getOwnPropertySymbols(b).filter(function(w){return Object.getOwnPropertyDescriptor(b,w).enumerable}))),v.forEach(function(w){ze(y,w,b[w])})}return y}function ze(y,_,b){return _ in y?Object.defineProperty(y,_,{value:b,enumerable:!0,configurable:!0,writable:!0}):y[_]=b,y}var q=function(_,b){var v=se({},_,b.attrs);return l($e,se({},v,{icon:Re}),null)};q.displayName="BugOutlined";q.inheritAttrs=!1;const Fe=q;const u=y=>(Ve("data-v-d059e27f"),y=y(),Be(),y),He={class:"info-container"},Ie={class:"info-row"},Le=u(()=>t("div",{class:"info-label"},"工具名称:",-1)),je={class:"info-value"},Ue={class:"info-row"},qe=u(()=>t("div",{class:"info-label"},"工具描述:",-1)),Ke={class:"info-value"},Je={class:"info-row"},Ge=u(()=>t("div",{class:"info-label"},"状态:",-1)),Qe={class:"info-value"},We={class:"info-row"},Xe=u(()=>t("div",{class:"info-label"},"当前版本:",-1)),Ye={class:"info-value"},Ze={class:"info-row"},et=u(()=>t("div",{class:"info-label"},"创建时间:",-1)),tt={class:"info-value"},st={class:"info-row"},ot=u(()=>t("div",{class:"info-label"},"更新时间:",-1)),at={class:"info-value"},lt={class:"info-row"},nt=u(()=>t("div",{class:"info-label"},"创建人:",-1)),it={class:"info-value"},rt={class:"info-row"},ct=u(()=>t("div",{class:"info-label"},"更新人:",-1)),dt={class:"info-value"},ut={key:1,class:"parameters-container"},vt={class:"parameter-header"},_t={class:"parameter-name"},pt={key:0,class:"parameter-description"},ft={key:1,class:"parameter-details"},mt={key:0,class:"parameter-enum"},ht=u(()=>t("span",{class:"parameter-detail-label"},"可选值:",-1)),yt={class:"parameter-enum-values"},gt={key:1,class:"parameter-format"},bt=u(()=>t("span",{class:"parameter-detail-label"},"格式:",-1)),kt={class:"parameter-detail-value"},wt={key:2,class:"parameter-items"},$t=u(()=>t("span",{class:"parameter-detail-label"},"元素类型:",-1)),Ct={class:"parameter-detail-value"},xt={key:3,class:"parameter-default"},Ot=u(()=>t("span",{class:"parameter-detail-label"},"默认值:",-1)),St={class:"parameter-detail-value"},Mt={key:4,class:"parameter-min"},Vt=u(()=>t("span",{class:"parameter-detail-label"},"最小值:",-1)),Bt={class:"parameter-detail-value"},Pt={key:5,class:"parameter-max"},Dt=u(()=>t("span",{class:"parameter-detail-label"},"最大值:",-1)),Et={class:"parameter-detail-value"},Tt={key:6,class:"parameter-min-length"},At=u(()=>t("span",{class:"parameter-detail-label"},"最小长度:",-1)),Nt={class:"parameter-detail-value"},Rt={key:7,class:"parameter-max-length"},zt=u(()=>t("span",{class:"parameter-detail-label"},"最大长度:",-1)),Ft={class:"parameter-detail-value"},Ht={class:"code-container"},It={class:"code-display"},Lt={key:1,class:"version-list"},jt={class:"version-row"},Ut=u(()=>t("div",{class:"version-label"},"版本:",-1)),qt=u(()=>t("div",{class:"version-label time-label"},"发布时间:",-1)),Kt={class:"version-time"},Jt={key:0,class:"version-description"},Gt={class:"code-display"},Qt={class:"rollback-warning"},Wt={class:"rollback-code-container"},Xt={class:"rollback-code-header"},Yt={class:"code-display"},Zt=["onClick"],es={class:"config-description"},ts=["onClick"],ss={class:"func-description"},os={__name:"ToolDetailView",setup(y){const _=xe(),b=Oe(),v=Z(()=>b.params.id),w=d(!1),r=d(null),N=d([]),K=d(!1),oe=d(1),ae=d(10),le=d(0),J=d("basic"),R=d([]),z=d([]),E=d(!1),B=d(!1),$=d(null),O=d(""),ne=d(null),F=d(!1),H=d(!1),I=d(""),L=d(!1),C=Z(()=>!r.value||!r.value.parameters?null:r.value.parameters),ie=n=>!C.value||!C.value.required?!1:C.value.required.includes(n);Se(()=>{T(),G(),re(),ce()});const T=async()=>{w.value=!0;try{await k({method:"get",url:`/api/v1/tool/${v.value}`,onSuccess:n=>{r.value=n},errorMessage:"获取工具数据失败"})}finally{w.value=!1}},G=async()=>{K.value=!0;try{await k({method:"get",url:`/api/v1/tool/${v.value}/deploy/history`,params:{page:oe.value,size:ae.value},onSuccess:(n,a)=>{N.value=n,le.value=a.total},errorMessage:"获取发布历史失败"})}finally{K.value=!1}},re=async()=>{try{await k({method:"get",url:`/api/v1/tool/${v.value}/config`,onSuccess:n=>{R.value=n||[]},errorMessage:"获取绑定配置失败"})}catch(n){console.error("Error fetching bound configs:",n)}},ce=async()=>{try{await k({method:"get",url:`/api/v1/tool/${v.value}/func`,onSuccess:n=>{z.value=n||[]},errorMessage:"获取依赖函数失败"})}catch(n){console.error("Error fetching dependent functions:",n)}},de=async n=>{try{if($.value=n.version,n.code){O.value=n.code,E.value=!0;return}await k({method:"get",url:`/api/v1/tool/${v.value}/version/${n.version}`,onSuccess:a=>{O.value=a.code||"// No code available for this version",E.value=!0},errorMessage:"获取工具代码失败"})}catch(a){console.error("Error fetching version code:",a),ee.error("获取工具代码失败")}},ue=async n=>{try{if($.value=n.version,ne.value=n,n.code){O.value=n.code,B.value=!0;return}await k({method:"get",url:`/api/v1/tool/${v.value}/version/${n.version}`,onSuccess:a=>{O.value=a.code||"// No code available for this version",B.value=!0},errorMessage:"获取工具代码失败"})}catch(a){console.error("Error fetching version code:",a),ee.error("获取工具代码失败")}},ve=async()=>{if($.value){F.value=!0;try{await k({method:"post",url:`/api/v1/tool/${v.value}/deploy/rollback/${$.value}`,successMessage:"回滚成功",errorMessage:"回滚失败",onSuccess:()=>{T(),B.value=!1}})}catch(n){console.error("Error rolling back tool:",n)}finally{F.value=!1}}},_e=async()=>{L.value=!0;try{await k({method:"post",url:`/api/v1/tool/${v.value}/deploy`,data:{description:I.value},successMessage:"发布成功",errorMessage:"发布失败",onSuccess:()=>{H.value=!1,T(),G()}})}finally{L.value=!1}},Q=async n=>{try{const a=n?"enable":"disable";await k({method:"patch",url:`/api/v1/tool/${v.value}/${a}`,successMessage:`${n?"启用":"禁用"}成功`,errorMessage:`${n?"启用":"禁用"}失败`,onSuccess:()=>{T()}})}catch(a){console.error(`Error ${n?"enabling":"disabling"} tool:`,a)}};return(n,a)=>{const S=f("a-tag"),M=f("a-button"),pe=f("a-space"),V=f("a-tab-pane"),A=f("a-empty"),j=f("a-modal"),fe=f("a-alert"),W=f("a-list-item-meta"),X=f("a-list-item"),Y=f("a-list"),me=f("a-tabs"),he=f("a-card"),ye=f("a-textarea"),ge=f("a-form-item"),be=f("a-form");return o(),m(Pe,{"current-page-key":"tool"},{default:s(()=>[r.value?(o(),m(he,{key:0},{title:s(()=>[t("span",null,"工具详情 - "+i(r.value.name),1),r.value.current_version?(o(),m(S,{key:0,color:"blue",class:"version-tag"},{default:s(()=>[g("v"+i(r.value.current_version),1)]),_:1})):h("",!0)]),extra:s(()=>[l(pe,null,{default:s(()=>[l(M,{onClick:a[0]||(a[0]=e=>p(_).push("/tool"))},{icon:s(()=>[l(p(te))]),default:s(()=>[g(" 返回 ")]),_:1}),l(M,{type:"primary",onClick:a[1]||(a[1]=e=>p(_).push(`/tool/${v.value}/edit`))},{icon:s(()=>[l(p(Me))]),default:s(()=>[g(" 编辑 ")]),_:1}),l(M,{type:"primary",onClick:a[2]||(a[2]=e=>p(_).push(`/tool/${v.value}/debug`))},{icon:s(()=>[l(p(Fe))]),default:s(()=>[g(" 调试 ")]),_:1}),r.value.is_enabled?(o(),m(M,{key:0,type:"primary",danger:"",onClick:a[3]||(a[3]=e=>Q(!1))},{icon:s(()=>[l(p(Te))]),default:s(()=>[g(" 禁用 ")]),_:1})):(o(),m(M,{key:1,type:"primary",onClick:a[4]||(a[4]=e=>Q(!0))},{icon:s(()=>[l(p(Ae))]),default:s(()=>[g(" 启用 ")]),_:1}))]),_:1})]),default:s(()=>[l(me,{activeKey:J.value,"onUpdate:activeKey":a[7]||(a[7]=e=>J.value=e)},{default:s(()=>[l(V,{key:"basic",tab:"基本信息"},{default:s(()=>[t("div",He,[t("div",Ie,[Le,t("div",je,i(r.value.name),1)]),t("div",Ue,[qe,t("div",Ke,i(r.value.description||"无描述"),1)]),t("div",Je,[Ge,t("div",Qe,[l(S,{color:r.value.is_enabled?"green":"red"},{default:s(()=>[g(i(r.value.is_enabled?"已启用":"已禁用"),1)]),_:1},8,["color"])])]),t("div",We,[Xe,t("div",Ye,i(r.value.current_version||"未发布"),1)]),t("div",Ze,[et,t("div",tt,i(p(U)(r.value.created_at)),1)]),t("div",st,[ot,t("div",at,i(p(U)(r.value.updated_at)),1)]),t("div",lt,[nt,t("div",it,i(r.value.created_by||"-"),1)]),t("div",rt,[ct,t("div",dt,i(r.value.updated_by||"-"),1)])])]),_:1}),l(V,{key:"parameters",tab:"参数定义"},{default:s(()=>[!C.value||!C.value.properties||Object.keys(C.value.properties).length===0?(o(),m(A,{key:0,description:"无参数定义"})):(o(),c("div",ut,[(o(!0),c(P,null,D(C.value.properties,(e,x)=>(o(),c("div",{key:x,class:"parameter-item"},[t("div",vt,[t("span",_t,i(e.title||x),1),l(S,{class:"parameter-type"},{default:s(()=>[g(i(e.type||"string"),1)]),_:2},1024),ie(x)?(o(),m(S,{key:0,color:"red"},{default:s(()=>[g("必填")]),_:1})):h("",!0)]),e.description?(o(),c("div",pt,i(e.description),1)):h("",!0),e.enum||e.default!==void 0||e.minimum!==void 0||e.maximum!==void 0||e.format||e.items&&e.items.type?(o(),c("div",ft,[e.enum?(o(),c("div",mt,[ht,t("div",yt,[(o(!0),c(P,null,D(e.enum,(ke,we)=>(o(),m(S,{key:we,color:"blue"},{default:s(()=>[g(i(ke),1)]),_:2},1024))),128))])])):h("",!0),e.format?(o(),c("div",gt,[bt,t("span",kt,i(e.format),1)])):h("",!0),e.items&&e.items.type?(o(),c("div",wt,[$t,t("span",Ct,i(e.items.type),1)])):h("",!0),e.default!==void 0?(o(),c("div",xt,[Ot,t("span",St,i(JSON.stringify(e.default)),1)])):h("",!0),e.minimum!==void 0?(o(),c("div",Mt,[Vt,t("span",Bt,i(e.minimum),1)])):h("",!0),e.maximum!==void 0?(o(),c("div",Pt,[Dt,t("span",Et,i(e.maximum),1)])):h("",!0),e.minLength!==void 0?(o(),c("div",Tt,[At,t("span",Nt,i(e.minLength),1)])):h("",!0),e.maxLength!==void 0?(o(),c("div",Rt,[zt,t("span",Ft,i(e.maxLength),1)])):h("",!0)])):h("",!0)]))),128))]))]),_:1}),l(V,{key:"code",tab:"工具代码"},{default:s(()=>[t("div",Ht,[t("pre",It,i(r.value.code),1)])]),_:1}),l(V,{key:"history",tab:"发布历史"},{default:s(()=>[N.value.length?(o(),c("div",Lt,[(o(!0),c(P,null,D(N.value,e=>(o(),c("div",{key:e.id,class:"version-item"},[t("div",jt,[Ut,l(S,{class:"version-tag",color:r.value.current_version===e.version?"blue":"default",onClick:x=>de(e)},{default:s(()=>[g(" v"+i(e.version),1)]),_:2},1032,["color","onClick"]),qt,t("span",Kt,i(p(U)(e.created_at)),1),l(M,{type:"primary",size:"small",disabled:r.value.current_version===e.version,class:"rollback-button",onClick:x=>ue(e)},{icon:s(()=>[l(p(te))]),default:s(()=>[g(" 回滚 ")]),_:2},1032,["disabled","onClick"])]),e.description?(o(),c("div",Jt,i(e.description),1)):h("",!0)]))),128))])):(o(),m(A,{key:0,description:"无发布历史"})),l(j,{open:E.value,"onUpdate:open":a[5]||(a[5]=e=>E.value=e),title:`工具代码 (v${$.value})`,width:"800px",footer:null},{default:s(()=>[t("pre",Gt,i(O.value),1)]),_:1},8,["open","title"]),l(j,{open:B.value,"onUpdate:open":a[6]||(a[6]=e=>B.value=e),title:`确认回滚到版本 v${$.value}?`,width:"800px","confirm-loading":F.value,"ok-text":"确认回滚","cancel-text":"取消",onOk:ve},{default:s(()=>[t("div",Qt,[l(fe,{message:"警告",description:"回滚操作将使当前工具代码替换为选中版本的代码，请确认该操作。",type:"warning","show-icon":"",style:{"margin-bottom":"16px"}})]),t("div",Wt,[t("div",Xt,"版本 v"+i($.value)+" 的代码:",1),t("pre",Yt,i(O.value),1)])]),_:1},8,["open","title","confirm-loading"])]),_:1}),l(V,{key:"configs",tab:"绑定配置"},{default:s(()=>[R.value.length?(o(),m(Y,{key:1},{default:s(()=>[(o(!0),c(P,null,D(R.value,e=>(o(),m(X,{key:e.id},{default:s(()=>[l(W,null,{avatar:s(()=>[l(p(De),{class:"config-icon"})]),title:s(()=>[t("a",{class:"config-link",onClick:x=>p(_).push(`/config/${e.id}`)},i(e.name),9,Zt)]),description:s(()=>[t("div",es,i(e.description||"无描述"),1)]),_:2},1024)]),_:2},1024))),128))]),_:1})):(o(),m(A,{key:0,description:"无绑定配置"}))]),_:1}),l(V,{key:"funcs",tab:"依赖函数"},{default:s(()=>[z.value.length?(o(),m(Y,{key:1},{default:s(()=>[(o(!0),c(P,null,D(z.value,e=>(o(),m(X,{key:e.id},{default:s(()=>[l(W,null,{avatar:s(()=>[l(p(Ee),{class:"func-icon"})]),title:s(()=>[t("a",{class:"func-link",onClick:x=>p(_).push(`/func/${e.id}`)},i(e.name),9,ts)]),description:s(()=>[t("div",ss,i(e.description||"无描述"),1)]),_:2},1024)]),_:2},1024))),128))]),_:1})):(o(),m(A,{key:0,description:"无依赖函数"}))]),_:1})]),_:1},8,["activeKey"])]),_:1})):h("",!0),l(j,{open:H.value,"onUpdate:open":a[9]||(a[9]=e=>H.value=e),title:"发布工具","confirm-loading":L.value,"ok-text":"确定","cancel-text":"取消",onOk:_e},{default:s(()=>[l(be,{layout:"vertical"},{default:s(()=>[l(ge,{label:"发布描述"},{default:s(()=>[l(ye,{value:I.value,"onUpdate:value":a[8]||(a[8]=e=>I.value=e),rows:4,placeholder:"请输入发布描述"},null,8,["value"])]),_:1})]),_:1})]),_:1},8,["open","confirm-loading"])]),_:1})}}},ds=Ce(os,[["__scopeId","data-v-d059e27f"]]);export{ds as default};
