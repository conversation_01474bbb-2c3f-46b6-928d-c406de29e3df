# Easy MCP 快速开始指南

> **注意**: 确保 `static/` 目录包含前端构建文件，所有 Docker 配置文件已统一放置在 `docker/` 目录下。

## 🚀 一键部署

### 使用 SQLite（推荐新手）

```bash
# 克隆项目
git clone <repository-url>
cd easy-mcp

# 一键部署
./easy-mcp.sh deploy

# 访问应用
open http://localhost:8000
```

### 使用 PostgreSQL（推荐生产环境）

```bash
# 部署 PostgreSQL 版本
./easy-mcp.sh -d postgres deploy

# 访问应用
open http://localhost:8000
```

### 使用 MySQL

```bash
# 部署 MySQL 版本
./easy-mcp.sh -d mysql deploy

# 访问应用
open http://localhost:8000
```

## 📋 前置要求

- Docker 20.10+
- Docker Compose 2.0+
- 8GB+ 可用内存
- 10GB+ 可用磁盘空间

## 🔧 常用命令

```bash
# 查看服务状态
./easy-mcp.sh status

# 查看日志
./easy-mcp.sh logs

# 重启服务
./easy-mcp.sh restart

# 停止服务
./easy-mcp.sh stop

# 清理所有数据（谨慎使用）
./easy-mcp.sh clean
```

## 🗄️ 数据库管理

### SQLite 备份与恢复

```bash
# 备份数据库
./easy-mcp.sh backup

# 恢复数据库
./easy-mcp.sh restore
```

### PostgreSQL/MySQL 备份

```bash
# PostgreSQL 备份
docker exec easy-mcp-postgres pg_dump -U easy_mcp easy_mcp > backup.sql

# MySQL 备份
docker exec easy-mcp-mysql mysqldump -u easy_mcp -peasy_mcp_password easy_mcp > backup.sql
```

## 🔐 默认账户

- **用户名**: admin
- **密码**: admin123
- **邮箱**: <EMAIL>

> ⚠️ **生产环境请务必修改默认密码！**

## 🌐 访问地址

- **Web 界面**: http://localhost:8000
- **API 文档**: http://localhost:8000/docs
- **PostgreSQL**: localhost:5432
- **MySQL**: localhost:3306

## 🛠️ 故障排除

### 端口冲突

```bash
# 检查端口占用
netstat -tulpn | grep :8000

# 停止占用端口的服务
sudo lsof -ti:8000 | xargs kill -9
```

### 权限问题

```bash
# 修复脚本权限
chmod +x easy-mcp.sh

# 修复数据目录权限
sudo chown -R $USER:$USER data logs backup
```

### 容器启动失败

```bash
# 查看详细日志
./easy-mcp.sh logs

# 重新构建镜像
./easy-mcp.sh build

# 清理后重新部署
./easy-mcp.sh clean
./easy-mcp.sh deploy
```

## 📚 更多信息

- [Docker说明.md](doc/Docker说明.md) - 完整部署文档
- [系统设计.md](doc/系统设计.md) - 系统架构设计
- [数据库设计.md](doc/数据库设计.md) - 数据库设计文档
- [接口说明.md](doc/接口说明.md) - API接口文档
- [API 文档](http://localhost:8000/docs) - 在线API文档

## 🆘 获取帮助

```bash
# 查看 CLI 帮助
./easy-mcp.sh --help

# 使用 Makefile
make help
```
